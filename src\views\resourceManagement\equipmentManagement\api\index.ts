import http, { resolveFunc, rejectFunc, EnumAim, XMLHttpDownload2, EnumContentType } from '@/utils/axios';
import * as Types from './types';
import { isDev } from '@/config';
const aim = EnumAim.test;

export enum Api {
	getList = '/web-api/equipment/v1/page', //  分页查询表格数据
	saveCenter = '/web-api/equipment/v1/save', //  保存服务队人员
	editCenter = '/web-api/equipment/v1/modify', //  修改服务队人员
	deleteCenter = '/web-api/equipment/v1/', //  删除服务队人员
	importCenter = '/web-api/equipment/v1/import', //  导入服务队人员
	bind = '/web-api/equipment/v1/bind/', //  绑定设备
	unbinding = '/web-api/equipment/v1/unbinding/', //  解绑设备
	status = '/web-api/equipment/v1/status', //  批量开关设备
	getTeamList = '/web-api/service-center/v1/list/for-relevancy/team/region', //
	bindDeviceList = '/web-api/iip/v1/device/page/list', //  机具管理——绑定设备——设备列表
	batchDelete = '/web-api/equipment/v1/remove', //  机具管理——批量删除/单独删除
}
export function api_getList(param: Types.getListRequestType): Promise<Types.PageResultEquipmentBaseDTO> {
	const url = Api.getList;
	return http.get(url, param, { aim, loading: true }).then((res) => res[0] || {});
}

export function api_saveCenter(param: Types.SaveEquipment): Promise<any> {
	const url = Api.saveCenter;
	return http.post(url, param, { aim, successTips: true, loading: true });
}

export function api_editCenter(param: Types.SaveEquipment): Promise<any> {
	const url = Api.editCenter;
	return http.post(url, param, { aim, successTips: true, loading: true });
}

export function api_delete(serviceUuid: string) {
	const url = Api.deleteCenter + serviceUuid;
	return http.delete(url, null, { aim, successTips: false, loading: true });
}

export function api_import(file: any) {
	const url = Api.importCenter;
	return http.post(
		url,
		{ file: file },
		{ aim, contentType: EnumContentType.FORM_DATA, successTips: true, loading: true, toFormData: true },
	);
}

export function api_bind(uuid: string, deviceNumber: string): Promise<any> {
	const url = Api.bind + uuid + '/' + deviceNumber;
	return http.post(url, null, { aim, successTips: true, loading: true });
}
export function api_unbind(uuid: string): Promise<any> {
	const url = Api.unbinding + uuid;
	return http.post(url, null, { aim, successTips: true, loading: true });
}
export function api_status(param: Types.RequestStatus): Promise<any> {
	const url = Api.status;
	return http.post(url, param, { aim, successTips: true, loading: true });
}
export function api_getTeamList(param: Types.RequestTeamList): Promise<Types.TeamList[]> {
	const url = Api.getTeamList;
	return http.get(url, param, { aim, loading: true }).then((res) => res || []);
}

// 机具管理——绑定设备——设备列表
export function api_getBindDeviceList(number: string,tenantId: string): Promise<Types.BindDeviceList[]> {
	const url = Api.bindDeviceList;
	return http.get(url, {number,tenantId}, { aim, loading: true }).then((res) => res || []);
}

// 机具管理——批量删除/单独删除
export function api_batchDelete(data: any) {
	const url = Api.batchDelete;
	return http.delete(url, data, { aim, successTips: false, loading: true });
}