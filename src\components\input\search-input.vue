// 样式在index.vue中
<template>
	<el-input
		:modelValue="value_"
		@input="onInput"
		class="n-t-input-box"
		clearable
		:maxlength="maxLength"
		@clear="onClear"
		@keydown.enter="onSearch"
		:placeholder="placeholder"
	>
		<template #[slotName]>
			<el-icon class="t-input-icon" @click="onSearch" title="搜索"><Search /></el-icon>
		</template>
	</el-input>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
interface Props {
	value: string;
	placeholder?: string;
	maxLength?: number;
	slotName?: 'prefix' | 'suffix';
}
const props = withDefaults(defineProps<Props>(), {
	maxLength: 200,
	slotName: 'suffix',
	placeholder: '关键字搜索',
});
const emit = defineEmits<{
	(ev: 'update:value', value: string): void;
	(ev: 'search'): void; // 当 移交管理员，退出窗口时，触发这个事件
}>();
const value_ = ref('');
watch(
	() => props.value,
	(val) => {
		value_.value = val;
	},
);
function onInput(val: string) {
	// console.log('111111111111111111111111', val);
	value_.value = val;
	emit('update:value', val);
}
function onClear() {
	value_.value = '';
	emit('update:value', '');
	emit('search');
}
function onSearch() {
	emit('search');
}
</script>
<style lang="scss" scoped>
.n-t-input-box {
	flex-shrink: 0;
	width: auto;
}
.t-input-icon {
	order: 1;
	cursor: pointer;
}
</style>
