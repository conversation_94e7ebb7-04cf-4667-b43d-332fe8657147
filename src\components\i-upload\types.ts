import IUpload from './index.vue';
import { Props } from './index.vue';

declare global {
	type UploadInstance = InstanceType<typeof IUpload>;
	interface UploadProps extends Props {}
	interface EventUploadChange {
		successFileList: Array<ResponseUpload>;
		successFiles: File[]; // 原生的文件对象，预览时使用
		errorFileList: UploadErrorFile[];
	}

	// Omit 以一个类型为基础支持剔除某些属性，然后返回一个新类型。
	interface UploadErrorFile {
		fileName: string; //  '测试视频0001';
		fileSize: number; //  291089;
		fileType: string; //  'mp4';
		message: string; // 失败原因
	}
}
