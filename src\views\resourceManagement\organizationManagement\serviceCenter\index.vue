<template>
	<container-lcr ref="refContainer" :title="false" :topHeight="64">
		<template #top>
			<div style="width: 50%; height: 32px" class="form-b">
				<el-form class="g-form-box" ref="refForm" :model="formState" label-position="left">
					<el-form-item class="g-col-50" label="服务中心名称" prop="key">
						<el-input v-model="formState.key" placeholder="请输入服务中心名称" />
					</el-form-item>
					<el-form-item class="g-col-50" label="行政区划" prop="region">
						<el-cascader
							placeholder="请选择行政区划"
							:props="{
								value: 'areaId',
								label: 'areaName',
								children: 'children',
							}"
							style="width: 100%"
							v-model="formState.region"
							:options="areas"
							@change="handleChange"
						/>
					</el-form-item>
				</el-form>
			</div>
			<el-button type="primary" @click="clickSearchBtn">查询</el-button>
			<el-button type="" @click="clickResetBtn">重置</el-button>
		</template>
		<div class="table-box-wrap">
			<common-table
				:otherHeight="416"
				ref="refTable"
				:columns="columnsCommon"
				:getList="getList"
				:total="pageTotal"
				:data="tableData"
				:getBtns="btns"
				:operateWidth="250"
				:show-check-box="true"
				@selection-change="onChangeSelection"
			>
				<template #header>
					<div class="btn-box">
						<el-button type="primary" @click="clickAddCenterBtn" :icon="Plus">新增</el-button>
						<i-upload
							style="margin: 0 12px"
							:multiple="false"
							:accept="['xls', 'xlsx']"
							:fileSize="50"
							:beforeUpload="uploadBefore"
						>
							<el-button @click="" :icon="Download">批量导入</el-button>
						</i-upload>
						<el-button @click="getImportTemeplete" :icon="Download">导入模板</el-button>
						<el-popconfirm title="是否确定要删除所选数据？" @confirm="clickDeleteMore">
							<template #reference>
								<el-button :disabled="multipleSelection.length === 0" :icon="Delete">批量删除</el-button>
							</template>
						</el-popconfirm>
						<el-button @click="clickExportTable" :icon="Upload">导出</el-button>
					</div>
				</template>
			</common-table>
		</div>
		<AddCenterDialog
			:rowData="activeRow"
			:type="dialogType"
			v-model:visible="openBaseDialog"
			@saveForm="refreshTableData"
		></AddCenterDialog>
		<BasicInfo :rowData="activeRow" v-model:visible="openBasicInfoDialog"></BasicInfo>
	</container-lcr>
</template>

<script lang="ts" setup>
import { cloneDeep } from 'lodash-es';
import { onMounted, ref } from 'vue';
import { Delete, Upload, Plus, Download } from '@element-plus/icons-vue';
import { api_delete, api_getList, api_import, api_saveCenter } from './api/index';
import { PageResultServiceCenterBaseVO, ServiceCenterBaseVO, getListRequestType, RequestSaveCenter } from './api/types';
import AddCenterDialog from './components/addCenterDialog.vue';
import BasicInfo from './components/basicInfo.vue';
import { watch } from 'vue';
import { api_getFileTemeplete } from '@/api';
import { XMLHttpDownload2 } from '@/utils/axios';
import { api_getArea, api_getDict } from '@/api/dict';
import { exportToExcelWithCustomHeaders } from '@/utils';
import { storeUserInfo } from '@/store';
import { store } from '@/store';
const columnsCommon: CommonTableColumn<ServiceCenterBaseVO>[] = [
	{
		label: '中心名称',
		prop: 'name',
		minWidth: 210,
	},
	{
		label: '联系人',
		prop: 'contactName',
	},
	{
		label: '联系方式',
		prop: 'contactTelephone',
		minWidth: 120,
	},
	{
		label: '详细地址',
		prop: 'addressDetail',
		minWidth: 210,
	},
	{
		label: '服务类型',
		prop: 'serviceScope',
		minWidth: 210,
		formatter(row, column, cellValue, index) {
			let str = '';
			let typeValue = cellValue.split(',');
			typeValue.forEach((r: string, index: number) => {
				center_service_type.value.forEach((d) => {
					if (d.code == r) str += d.name;
					if (index != typeValue.length - 1 && d.code == r) str += '、';
				});
			});
			return str;
		},
	},
];
const tableData = ref<ServiceCenterBaseVO[]>([]);
const oldParam = ref<getListRequestType>({
	pageNo: 1,
	pageSize: 20,
	name: '',
	region: '',
});

const pageTotal = ref(200);
const activeRow = ref<ServiceCenterBaseVO | null>(null);
const btns: OptionBtn<ServiceCenterBaseVO>[] = [
	{
		label: '查看',
		onClick(row) {
			dialogType.value = 'view';
			activeRow.value = row;
			openBaseDialog.value = true;
		},
	},
	{
		label: '编辑',
		onClick(row) {
			dialogType.value = 'edit';
			activeRow.value = row;
			openBaseDialog.value = true;
		},
	},
	{
		label: '人员机具明细',
		onClick(row) {
			activeRow.value = row;
			openBasicInfoDialog.value = true;
		},
	},
	{
		label: '删除',
		tips: '确定删除吗？',
		onClick(row) {
			api_delete(row.uuid!).then((res) => {
				ElMessage.success('操作成功');
				refTable.value?.refreshList();
			});
		},
	},
];
const refTable = ref<CommonTableInstance>();
function getList(param?: CommonTableParams) {
	let data = {
		pageNo: param?.pageNumber || 1,
		pageSize: param?.pageSize || 20,
		provinceCode: formState.value.region[0],
		cityCode: formState.value.region[1] ? formState.value.region[1] : '',
		districtCode: formState.value.region[2] ? formState.value.region[2] : '',
		key: formState.value.key,
		tenantId: storeUserInfo.enterpriseId,
	};
	return api_getList(data).then((info) => {
		tableData.value = info.list;
		pageTotal.value = info.total;
	});
}
const center_service_type = ref<DicDataItemCode[]>([]);
const areas = ref<AreaTreeBase[]>([]);
onMounted(() => {
	api_getDict('center_service_type').then((res) => {
		center_service_type.value = res;
	});
	api_getArea().then((res) => {
		areas.value = res;
	});
	refTable.value?.refreshList();
});
const handleChange = (value) => {
	formState.value.region = value;
};
function clickSearchBtn() {
	refTable.value?.refreshList();
}
function clickResetBtn() {
	formState.value = cloneDeep(defaultForm);
	refTable.value?.refreshList();
}
const refContainer = ref<ContainerLcrInstance>();

interface quereType {
	key: string;
	region: string[];
}
const refForm = ref<ElFormInstance>();
const defaultForm: quereType = {
	key: '', // 	* 名称
	region: [],
};
const formState = ref<quereType>(cloneDeep(defaultForm));

const openBaseDialog = ref<boolean>(false); //是否打开新增弹窗
const openBasicInfoDialog = ref<boolean>(false); //是否打开人员机具弹窗
const dialogType = ref('add');
function clickAddCenterBtn() {
	dialogType.value = 'add';
	openBaseDialog.value = true;
}
function refreshTableData() {
	refTable.value?.refreshList();
}
const multipleSelection = ref<ServiceCenterBaseVO[]>([]);
function onChangeSelection(rows: ServiceCenterBaseVO[]) {
	multipleSelection.value = rows;
}

function clickDeleteMore() {
	let num = multipleSelection.value.length;
	multipleSelection.value?.forEach((row) => {
		api_delete(row.uuid!).then((res) => {
			num--;
			if (num === 0) {
				ElMessage.success('操作成功');
				refTable.value?.refreshList();
			}
		});
	});
}
function uploadBefore(files: File[]) {
	console.log(files);
	if (files && files.length) {
		let file = files[0];
		api_import(file, storeUserInfo.enterpriseId).then(() => {
			refTable.value?.resetList();
		});
	}
	return Promise.reject(false);
}
function getImportTemeplete() {
	XMLHttpDownload2('/web-api/file/v1/template?fileName=center.xlsx', {
		fileName: '服务中心导入模板.xlsx',
	}).then((res) => {
		console.log(res);
	});
}

function clickExportTable() {
	// 使用示例
	const headers = {
		name: '中心名称',
		contactName: '联系人',
		contactTelephone: '联系方式',
		addressDetail: '详细地址',
		serviceScope: '服务类型',
	};
	let exportArr = multipleSelection.value.length == 0 ? tableData.value : multipleSelection.value;
	let data = exportArr.map((d) => {
		let str = '';
		let typeValue = d.serviceScope!.split(',');
		typeValue.forEach((r: string, index: number) => {
			center_service_type.value.forEach((d) => {
				if (d.code == r) str += d.name;
				if (index != typeValue.length - 1 && d.code == r) str += '、';
			});
		});
		d.serviceScope = str;
		return d;
	});
	exportToExcelWithCustomHeaders(data, headers, '服务中心.xlsx', {
		columnWidths: [40, 20, 20, 60, 60],
	});
}
</script>
<style lang="scss" scoped>
.query-box {
	display: flex;
	padding: 16px;
	box-sizing: border-box;
}
</style>
