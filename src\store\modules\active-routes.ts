import { defineStore } from 'pinia';
import { RouteRecordRaw } from 'vue-router';

interface RoutesState {
	routes: RouteRecordRaw[]; // 活跃的路由
}
export const useStoreActiveRoutes = defineStore({
	id: 'active-routes',
	state: (): RoutesState => {
		return {
			routes: [],
		};
	},
	getters: {
		getRouteNames(): string[] {
			return this.routes.map((item) => item.name as string);
		},
		getRoutes(): RouteRecordRaw[] {
			return this.routes;
		},
	},
	actions: {
		setRoute(item: RouteRecordRaw) {
			const has = this.routes.find((item_) => item_.name === item.name);
			if (!has) {
				this.routes.push(item);
			}
		},
		// 关闭当前
		removeRoute(item: RouteRecordRaw | string) {
			if (typeof item === 'string') {
				this.routes = this.routes.filter((item_) => item_.name !== item);
			} else {
				this.routes = this.routes.filter((item_) => item_.name !== item.name);
			}
		},
		// 关闭其它
		removeOther(item: RouteRecordRaw | string) {
			if (typeof item === 'string') {
				this.routes = this.routes.filter((item_) => item_.name === item);
			} else {
				this.routes = this.routes.filter((item_) => item_.name === item.name);
			}
		},
		clear() {
			this.routes = [];
		},
	},
});
