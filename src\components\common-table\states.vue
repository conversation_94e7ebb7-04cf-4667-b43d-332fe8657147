<template>
	<template v-if="!obj">-</template>
	<template v-else-if="!obj.color">{{ obj.label }}</template>
	<template v-else>
		<div class="t-states-box">
			<div class="t-states-icon" :style="{ 'background-color': mapColor[obj.color] || obj.color }"></div>
			{{ obj.label }}
		</div>
	</template>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { StatesItem, TypeStatesColor } from './type';
import { mapColor } from './common';

interface Props {
	prop: string | number;
	states: StatesItem[];
	row: any;
}
const props = withDefaults(defineProps<Props>(), {});

const obj = computed(() => {
	// 经过测试 在component组件的is中使用时，不能直接返回字符串，
	const prop = props.prop;
	const row = props.row || {};
	const states = props.states;
	if (!prop || row[prop || ''] === '' || row[prop || ''] === undefined || row[prop || ''] === null) return null;
	return states.find((item) => item.value === row[prop]) || null;
});
</script>
<style lang="scss" scoped>
.t-states-box {
	.t-states-icon {
		display: inline-block;
		height: 10px;
		width: 10px;
		border-radius: 50%;
		margin-right: 3px;
		// margin-bottom: 1px;
	}
}
</style>
