{"name": "ljy-screen", "version": "0.1.0", "private": true, "scripts": {"dev": "vite --host", "build": "vite build --mode production", "type-check": "vue-tsc --noEmit"}, "dependencies": {"axios": "1.7.2", "echarts": "5.5.1", "element-plus": "2.7.6", "file-saver": "^2.0.5", "js-md5": "0.8.3", "lodash-es": "4.17.21", "moment": "2.30.1", "pinia": "2.1.7", "vue": "3.4.31", "vue-qr": "4.0.9", "vue-router": "4.4.0", "xlsx": "0.18.5"}, "devDependencies": {"@types/node": "14.18.63", "@vitejs/plugin-legacy": "4.1.1", "@vitejs/plugin-vue": "4.6.2", "@vitejs/plugin-vue-jsx": "3.1.0", "sass": "1.77.6", "typescript": "5.5.3", "unplugin-auto-import": "0.17.6", "unplugin-vue-components": "0.27.2", "vite": "4.5.3", "vue-tsc": "1.2.0"}}