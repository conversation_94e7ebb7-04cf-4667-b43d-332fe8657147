html,
body {
	margin: 0;
	border: 0;
	padding: 0;
	background: #f7f8fa;
}

/*滚动条凹槽的颜色，还可以设置边框属性 */
*::-webkit-scrollbar-track-piece {
	background-color: #f6f6f6;
	-webkit-border-radius: 2em;
	-moz-border-radius: 2em;
	border-radius: 2em;
}
/*滚动条的宽度*/
*::-webkit-scrollbar {
	width: 9px;
	height: 9px;
}
/*滚动条的设置*/
*::-webkit-scrollbar-thumb {
	background-color: #e7e7e7;
	background-clip: padding-box;
	-webkit-border-radius: 2em;
	-moz-border-radius: 2em;
	border-radius: 2em;
}
/*滚动条鼠标移上去*/
*::-webkit-scrollbar-thumb:hover {
	background-color: #bbb;
}
*:focus-visible {
	outline: unset;
}
// @import './reset.less';
// 全局共用类
// i开头的表示某个组件中使用的类， 不是公共的
// g开头的表示公共全局类，
.g-icon-text {
	line-height: 1;
	display: inline-flex;
	align-items: center;
	> .el-icon {
		margin-right: 4px;
	}
}
// 单行，超出显示省略号
.g-ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
// 强制断行
.g-break-all {
	white-space: normal;
	word-wrap: break-word;
	word-break: break-all;
}
.g-click {
	color: var(--el-color-primary);
	cursor: pointer;
}

body {
	font-size: 16px;
	color: var(--el-text-color-primary);
}
// 一些宽度
.g-col-25 {
	width: 25% !important;
}
.g-col-33 {
	width: 33.33% !important;
}
.g-col-50 {
	width: 50% !important;
}
.g-col-66 {
	width: 66.66% !important;
}
.g-col-75 {
	width: 75% !important;
}
.g-col-100 {
	width: 100% !important;
}

.g-theme-text-color {
	color: var(--el-color-primary);
}

// 重置 element-plus的全局css变量
html {
	.el-table {
		--el-table-header-bg-color: #e5e6eb;
		.el-table__body {
			.el-table__cell {
				&.el-table__expanded-cell {
					padding-left: 10px;
					padding-right: 10px;
				}
				.cell {
					&:empty::after {
						content: '-';
					}
					overflow: visible;
					&.el-tooltip {
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
			}
		}
		> .el-popper {
			max-width: 500px;
			white-space: normal;
			word-wrap: break-word;
			word-break: break-all;
		}
	}
	.el-form {
		.el-input-number,
		.el-date-editor {
			width: 100%;
		}
		.el-input__inner {
			text-align: left;
		}
	}
	.el-tabs {
		&.g-tabs {
			.el-tabs__header {
				margin-bottom: 0;
			}
		}
	}
	.el-button {
		&:focus-visible {
			outline: unset;
		}
	}
	.el-dialog__body {
		max-height: calc(100vh - 128px);
		overflow: auto;
	}
	.el-message-box__message {
		overflow: auto;
	}
	// 全局表单的样式
	.g-form-box {
		position: relative;
		display: flex;
		flex-wrap: wrap;
		align-content: flex-start;
		> .el-form-item {
			width: 100%;
			// flex-grow: 1;
			padding: 0 20px;
			padding-bottom: 0;
			margin-bottom: 20px;
			box-sizing: border-box;
		}
	}
	&:root {
		--el-color-primary: rgba(19, 108, 77, 1);
		// --el-color-success: #40bb5a;
		// --el-color-danger: #f03342;
		// --el-color-error: #f03342;
		--el-text-color-primary: #1d2129;
		--el-menu-hover-bg-color: #EDF7F4;
		--el-menu-item-height: 40px;
	}
}
