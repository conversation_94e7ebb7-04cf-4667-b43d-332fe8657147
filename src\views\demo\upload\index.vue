<template>
	<h1>基础文件上传</h1>
	<h4>
		该组件仅负责基础上传功能，将文件上传的指定服务器。可以提示上传结果和显示上传进度，也可以分段上传（需要接口支持）。
		如果需要文件列表或者更复杂的交互，需要自己写。也可以用element-plus的上传组件，或者看情况再封装新组件。
	</h4>

	<h3>基础上传</h3>
	<i-upload :multiple="true" :accept="['jpg', 'png']" :fileSize="5" @change="onChange1">
		<el-button type="primary">上传文件</el-button>
	</i-upload>
	<h3>拖拽上传</h3>
	<i-upload :multiple="true" :drop="true" @change="onChange1">
		<div class="t-drop-box">拖拽文件到此区域</div>
	</i-upload>
</template>

<script lang="ts" setup>
function onChange1(info: EventUploadChange) {
	console.log('11111111111111111 onChange1', info.successFileList);
}
</script>
<style lang="scss" scoped>
.t-drop-box {
	width: 200px;
	height: 100px;
	border: 1px solid #d6d6d6;
	background-color: #eaeaea;
	text-align: center;
}
</style>
