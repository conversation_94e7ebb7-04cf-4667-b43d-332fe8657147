export interface getListRequestType {
	key?: string;
	pageNo: number;
	pageSize: number;
	[property: string]: any;
}

/**
 * PageResultServiceCenterBaseVO
 */
export interface PageResultServiceCenterBaseVO {
	list: ServiceCenterBaseVO[];
	total: number;
	[property: string]: any;
}

/**
 * ServiceCenterBaseVO
 */
export interface ServiceCenterBaseVO {
	addressDetail?: string; //详细地址
	centerParentUuid?: string; //服务中心uuid
	cityCode?: string; //地市编码
	contactName?: string; //联系人名称
	contactTelephone?: string; //联系人手机号
	creditCode?: string; //统一信用编码
	districtCode?: string; //区县编码
	latitude?: string; //维度
	longitude?: string; //经度
	name?: string; //区域服务中心名称
	provinceCode?: string; //省份编码
	remark?: string; //备注
	serviceScope?: string; //服务范围 0,1,2
	serviceType?: number; //0：服务中心 1：服务队
	uuid?: string; //唯一编码
	[property: string]: any;
}

/**
 * SaveCenterDTO 保存服务中心传参
 */

export interface RequestSaveCenter {
	addressDetail?: string; //详细地址
	centerParentUuid?: string; //服务中心uuid
	cityCode?: string; //地市编码
	contactName?: string; //联系人名称
	contactTelephone?: string; //联系人手机号
	creditCode?: string; //统一信用编码
	districtCode?: string; //区县编码
	latitude?: string; //维度
	longitude?: string; //经度
	name?: string; //区域服务中心名称
	provinceCode?: string; //省份编码
	remark?: string; //备注
	serviceScope?: string; // 服务范围 0,1,2
	[property: string]: any;
}

/**
 * PageResultCenterUserBaseVO
 */
export interface PageResultCenterUserBaseVO {
	list: CenterUserBaseVO[];
	total: number;
	[property: string]: any;
}

/**
 * CenterUserBaseVO
 */
export interface CenterUserBaseVO {
	centerId?: string; //区域化服务中心id
	centerName?: string; //服务中心名称
	cityCode?: string; //地市编码
	cityName?: string;
	districtCode?: string; //区县编码
	districtName?: string;
	provinceCode?: string; //省份编码
	provinceName?: string;
	remark?: string; //备注
	sysUserName?: string; //用户名称
	sysUserTelephone?: string; //用户手机号
	sysUserUuid?: string; //系统用户uuid
	uuid?: string;
	[property: string]: any;
}

/**
 * PageResultEquipmentBaseDTO
 */
export interface PageResultEquipmentBaseDTO {
	list: EquipmentBaseDTO[];
	total: number;
	[property: string]: any;
}

/**
 * EquipmentBaseDTO
 */
export interface EquipmentBaseDTO {
	bindStatus?: number; //绑定状态 ：0：未绑定  1：已绑定
	chassisNumber?: string; //底盘号
	/**
	 * 物联网iot设备类型
	 * MAGNETIC_CAR_POSITION  亚米级磁吸车载定位接收机
	 * PERSONNEL_HANDHELD_POSITION 4G全网通人员手持定位终端
	 * VIDEO_DEVICE  摄像头设备
	 * 设备状态：0关闭 1开启
	 */
	ciotModelType?: string;
	cityCode: string; //地市
	deviceModelName: string; //设备型号
	deviceNumber: string; //设备编码
	districtCode: string; //区县
	engineNumber?: string; //发动机编号
	equipmentBrand: string; //林机品牌
	equipmentCode: string; //林机类型编码
	factoryNumber: string; //出厂编号
	jobType: string; //作业类型
	latitude?: string; //纬度坐标
	longitude?: string; //经度坐标
	machineWidth: string; //林机幅宽(米)
	ownerType: number; //所属人类型：0组织 1个人
	ownerUuid: string; //服务队或个人uuid
	power: string; //功率(千瓦)
	provinceCode: string; //省
	remark?: string; //备注信息
	status?: number; //设备状态：0关闭 1开启
	terminalNumber?: string; //终端编号
	uuid: string; //唯一编码
	[property: string]: any;
}

export interface RequestCenterList {
	cityCode?: string;
	districtCode?: string;
	provinceCode?: string;
	tenantId?: string;
	[property: string]: any;
}

export interface CenterList {
	centerName: ''; //区域总面积
	centerUuid: ''; //区域完成总面积
	[property: string]: any;
}
