<template>
	<container-lcr ref="refContainer" :title="false" :topHeight="64">
		<template #top>
			<div style="width: 75%; height: 32px" class="form-b">
				<el-form class="g-form-box" ref="refForm" :model="formState" label-position="left">
					<el-form-item class="g-col-33" label="所属中心" prop="centerName">
						<el-input v-model="formState.centerName" placeholder="请输入中心名称" />
					</el-form-item>
					<el-form-item class="g-col-33" label="姓名" prop="userName">
						<el-input v-model="formState.userName" placeholder="请输入成员姓名" />
					</el-form-item>
					<el-form-item class="g-col-33" label="联系方式" prop="userTelephone">
						<el-input v-model="formState.userTelephone" placeholder="请输入联系方式" />
					</el-form-item>
				</el-form>
			</div>
			<el-button type="primary" @click="clickSearchBtn">查询</el-button>
			<el-button type="" @click="clickResetBtn">重置</el-button>
		</template>
		<div class="table-box-wrap">
			<common-table
				:otherHeight="416"
				ref="refTable"
				:columns="columnsCommon"
				:getList="getList"
				:total="pageTotal"
				:data="tableData"
				:getBtns="btns"
				:operateWidth="250"
				:show-check-box="true"
				@selection-change="onChangeSelection"
			>
				<template #header>
					<div class="btn-box">
						<el-button type="primary" @click="clickAddCenterBtn" :icon="Plus">新增</el-button>
						<!-- <el-button @click="" :icon="Download">批量导入</el-button> -->
						<i-upload
							style="margin: 0 12px"
							:multiple="false"
							:accept="['xls', 'xlsx']"
							:fileSize="50"
							:beforeUpload="uploadBefore"
						>
							<el-button @click="" :icon="Download">批量导入</el-button>
						</i-upload>
						<el-button @click="getImportTemeplete" :icon="Download">导入模板</el-button>
						<el-popconfirm title="是否确定要删除所选数据？" @confirm="clickDeleteMore">
							<template #reference>
								<el-button :disabled="multipleSelection.length === 0" :icon="Delete">批量删除</el-button>
							</template>
						</el-popconfirm>
						<el-button @click="clickExportTable" :icon="Upload">导出</el-button>
					</div>
				</template>
			</common-table>
		</div>
		<AddCenterDialog
			:rowData="activeRow"
			:type="dialogType"
			v-model:visible="openBaseDialog"
			@saveForm="refreshTableData"
		></AddCenterDialog>
	</container-lcr>
</template>

<script lang="ts" setup>
import { cloneDeep } from 'lodash-es';
import { onMounted, ref } from 'vue';
import { Delete, Upload, Plus, Download } from '@element-plus/icons-vue';
import { api_delete, api_getList, api_import } from './api/index';
import { CenterUserBaseVO, RequestSaveCenter } from './api/types';
import AddCenterDialog from './components/addCenterDialog.vue';
import { watch } from 'vue';
import { api_getFileTemeplete } from '@/api';
import { XMLHttpDownload2 } from '@/utils/axios';
import { exportToExcelWithCustomHeaders } from '@/utils';
import { storeUserInfo } from '@/store';
const columnsCommon: CommonTableColumn<CenterUserBaseVO>[] = [
	{
		label: '中心名称',
		prop: 'centerName',
		minWidth: 210,
	},
	{
		label: '归属服务队',
		prop: 'teamName',
		minWidth: 210,
	},
	{
		label: '姓名',
		prop: 'sysUserName',
	},
	{
		label: '联系方式',
		prop: 'userTelephone',
		minWidth: 120,
	},
	{
		label: '备注',
		prop: 'remark',
		minWidth: 210,
	},
];
const tableData = ref<CenterUserBaseVO[]>([]);

const pageTotal = ref(0);
const activeRow = ref<CenterUserBaseVO | null>(null);
const btns: OptionBtn<CenterUserBaseVO>[] = [
	{
		label: '编辑',
		onClick(row) {
			dialogType.value = 'edit';
			activeRow.value = row;
			openBaseDialog.value = true;
		},
	},
	{
		label: '删除',
		tips: '确定删除吗？',
		onClick(row) {
			api_delete(row.uuid!).then((res) => {
				ElMessage.success('操作成功');
				refTable.value?.refreshList();
			});
		},
	},
];
const refTable = ref<CommonTableInstance>();
function getList(param?: CommonTableParams) {
	let data = {
		pageNo: param?.pageNumber || 1,
		pageSize: param?.pageSize || 20,
		centerName: formState.value.centerName, // 	* 名称
		userName: formState.value.userName,
		userTelephone: formState.value.userTelephone,
		tenantId: storeUserInfo.enterpriseId,
	};
	return api_getList(data).then((info) => {
		tableData.value = info.list;
		pageTotal.value = info.total;
	});
}
onMounted(() => {
	refTable.value?.refreshList();
});
function clickSearchBtn() {
	refTable.value?.refreshList();
}
function clickResetBtn() {
	formState.value = cloneDeep(defaultForm);
	refTable.value?.refreshList();
}
const refContainer = ref<ContainerLcrInstance>();

interface quereType {
	centerName: string;
	userName: string;
	userTelephone: string;
}
const refForm = ref<ElFormInstance>();
const defaultForm: quereType = {
	centerName: '', // 	* 名称
	userName: '',
	userTelephone: '',
};
const formState = ref<quereType>(cloneDeep(defaultForm));

const openBaseDialog = ref<boolean>(false); //是否打开新增弹窗
const dialogType = ref('add');
function clickAddCenterBtn() {
	dialogType.value = 'add';
	openBaseDialog.value = true;
}
function refreshTableData() {
	refTable.value?.refreshList();
}
const multipleSelection = ref<CenterUserBaseVO[]>([]);
function onChangeSelection(rows: CenterUserBaseVO[]) {
	multipleSelection.value = rows;
}

function clickDeleteMore() {
	let num = multipleSelection.value.length;
	multipleSelection.value?.forEach((row) => {
		api_delete(row.uuid!).then((res) => {
			num--;
			if (num === 0) {
				ElMessage.success('操作成功');
				refTable.value?.refreshList();
			}
		});
	});
}
function uploadBefore(files: File[]) {
	console.log(files);
	if (files && files.length) {
		let file = files[0];
		api_import(file).then(() => {
			refTable.value?.resetList();
		});
	}
	return Promise.reject(false);
}
function getImportTemeplete() {
	XMLHttpDownload2('/web-api/file/v1/template?fileName=user.xlsx', {
		fileName: '服务中心导入模板.xlsx',
	}).then((res) => {
		console.log(res);
	});
}

function clickExportTable() {
	// 使用示例
	const headers = {
		centerName: '中心名称',
		teamName: '归属服务队',
		sysUserName: '姓名',
		userTelephone: '联系方式',
		remark: '备注',
	};
	let exportArr = multipleSelection.value.length == 0 ? tableData.value : multipleSelection.value;
 
	exportToExcelWithCustomHeaders(exportArr, headers, '服务队人员名单.xlsx', {
		columnWidths: [40, 40, 20, 20, 20],
	});
}
</script>
<style lang="scss" scoped>
.query-box {
	display: flex;
	padding: 16px;
	box-sizing: border-box;
}
</style>
