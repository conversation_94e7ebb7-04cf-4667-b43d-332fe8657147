<template>
	<el-form-item :rules="rules_" v-bind="$attrs" :disabled="disabled" ref="refDom">
		<i-upload v-bind="uploadProps || {}" :multiple="false" :accept="accept" :fileSize="fileSize" class="t-box" @change="onChange">
			<el-input v-model="value_" readonly @click.stop class="t-input" :disabled="disabled" :placeholder="placeholder" />
			<el-button>上传附件</el-button>
		</i-upload>
		<slot>
			<div class="t-text-1" v-if="text1">{{ text1 }}</div>
		</slot>
	</el-form-item>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { rule_true_change } from '@/utils/validator';

interface Props {
	modelValue?: string;
	fileObj?: ResponseFile | null;
	rules?: [Object, Array<any>, Boolean];
	required?: boolean;
	disabled?: boolean;
	placeholder?: string;
	accept?: string | string[]; // 文件格式 string 格式是原生格式， string[]是自定义格式，需要转成原生格式
	fileSize?: number; // 文件限制大小 单位 M
	uploadProps?: UploadProps;
}
const props = withDefaults(defineProps<Props>(), {
	placeholder: '请上传',
});

const emit = defineEmits<{
	(ev: 'update:modelValue', value: string): void;
	(ev: 'change', fileObj: ResponseFile | null): void; // 每次失去焦点时，都会触发这个事件
}>();
const refDom = ref();
const rules_ = computed<any>(() => {
	if ((props?.rules as unknown) === false) return undefined;
	if (!props.rules) {
		if (props.required) return rule_true_change;
		return undefined;
	}
	return props.rules;
});
const value_ = computed({
	get() {
		return props.modelValue;
	},
	set(value) {
		emit('update:modelValue', value!);
	},
});

const text1 = computed(() => {
	let texts: string[] = [];
	if (props.accept?.length) {
		if (typeof props.accept === 'string') {
			texts.push(`支持${props.accept}格式文件`);
		} else {
			texts.push(`支持${props.accept.join('、')}格式文件`);
		}
	}
	if (props.fileSize) {
		texts.push(`${props.fileSize}M以内`);
	}
	return texts.join('，');
});
function onChange(ev: EventUploadChange) {
	if (!ev.successFileList.length) return;
	const file = ev.successFileList[0].file || null;
	value_.value = `${file.fileName}.${file.fileType}`;
	emit('change', file);
}
</script>
<style lang="scss" scoped>
.t-box {
	width: 100%;
	display: flex;
	align-items: center;
	.t-input {
		flex-grow: 1;
		margin-right: 8px;
	}
}
.t-text-1 {
	color: var(--el-color-info-light-3);
	line-height: 1;
	margin-top: 14px;
}
</style>
