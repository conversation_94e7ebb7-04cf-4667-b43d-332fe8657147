<template>
	<h1>日期格式转换</h1>
	<h3>
		Day.js 是一个轻量的处理时间和日期的 JavaScript 库，和 Moment.js 的 API 设计保持完全一样。
		<a href="https://dayjs.fenxianglu.cn/" target="_blank">Day.js中文网</a>
	</h3>
	<h4>@/utils/utils-time 中封装了一些常用的方法</h4>
	{{ getDataStr(new Date()) }}
	<br />
	{{ getDataFormat('2022-11-01') }}
	<br />
	{{ getDataFormat('2023-11-01') }}
	<br />
	{{ getDataFormat(new Date()) }}

	<h3>日历表</h3>
	<div id="time">
		<button @click="switchLastMonth">上一月</button>
		<button @click="switchNextMonth">下一月</button>
	</div>
	<div class="t-box">
		<div class="t-li t-li-1">一</div>
		<div class="t-li t-li-1">二</div>
		<div class="t-li t-li-1">三</div>
		<div class="t-li t-li-1">四</div>
		<div class="t-li t-li-1">五</div>
		<div class="t-li t-li-1">六</div>
		<div class="t-li t-li-1">日</div>
		<div class="t-li t-li-2" v-for="item in daysValue">{{ item.key }}</div>
	</div>
</template>

<script lang="ts" setup>
import { getDataStr, getDataFormat } from '@/utils/utils-time';
import { ref } from 'vue';

interface DayObj {
	x: number; //
	y: number; //
	key: string; // 2024-01-01
	day: number; // 日期
	type: 'pre' | 'now' | 'next';
	tag?: string;
}
const daysValue = ref<DayObj[]>([]);
//每月天数
let fullDaysArr = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
let nowDiffMonth = 0; // 月份的差值 上个月就是 -1 下个月就是 1
let days: DayObj[] = [];
function updateDate(num) {
	nowDiffMonth += num;
	foreachDatePre();
	foreachDateNow();
	foreachDateNext();
	daysValue.value = days;
}
//切换到上个月
function switchLastMonth() {
	updateDate(-1);
}
//切换到下个月
function switchNextMonth() {
	updateDate(1);
}
//判断是否是闰年
function isLeapYear(year) {
	return year % 400 == 0 || (year % 4 == 0 && year % 100 != 0) ? 1 : 0;
}

//生成日期
function getLastDay(): DayObj {
	return (
		days[days.length - 1] || {
			x: -1, //
			y: -1, //
			key: '', // 2024-01-01
			day: -1, // 日期
			type: 'pre',
		}
	);
}
function foreachDateBefore(nowDate) {
	const newMonth = nowDate.getMonth();
	let fullDay = fullDaysArr[newMonth];
	if (newMonth === 1) {
		// 如果是2月份
		if (isLeapYear(nowDate.getFullYear()) == 1) {
			// 闰年 二月 29天
			fullDay++;
		}
	}

	return {
		fullDay,
		newMonth,
	};
}
// 渲染上个月的数据
function foreachDatePre() {
	const nowDate = new Date();
	nowDate.setMonth(nowDate.getMonth() + nowDiffMonth - 1, 1);
	const { fullDay, newMonth } = foreachDateBefore(nowDate);
	const lastDate = new Date(nowDate.getFullYear(), newMonth, fullDay);
	const numDay = lastDate.getDay() || 0;
	const key_ = `${nowDate.getFullYear()}-${newMonth + 1}`;
	days = [];
	for (let i = numDay - 1; i >= 0; i--) {
		const lastDay = getLastDay();
		days.push({
			x: (lastDay.x + 1) % 7, //
			y: Math.ceil((days.length + 1) / 7), //
			key: `${key_}-${fullDay - i}`, // 2024-01-01
			day: fullDay - i, // 日期
			type: 'pre',
		});
	}
}
// 渲染当前月数据
function foreachDateNow() {
	const nowDate = new Date();
	nowDate.setMonth(nowDate.getMonth() + nowDiffMonth, 1);
	const { fullDay, newMonth } = foreachDateBefore(nowDate);
	const numDay = fullDay;
	const key_ = `${nowDate.getFullYear()}-${newMonth + 1}`;
	for (let i = 1; i <= numDay; i++) {
		const lastDay = getLastDay();
		days.push({
			x: (lastDay.x + 1) % 7, //
			y: Math.ceil((days.length + 1) / 7), //
			key: `${key_}-${i}`, // 2024-01-01
			day: i, // 日期
			type: 'now',
		});
	}
}

// 渲染下个月数据
function foreachDateNext() {
	const nowDate = new Date();
	nowDate.setMonth(nowDate.getMonth() + nowDiffMonth + 1, 1);
	const newMonth = nowDate.getMonth();
	const numDay = 42 - days.length;
	const key_ = `${nowDate.getFullYear()}-${newMonth + 1}`;
	for (let i = 1; i <= numDay; i++) {
		const lastDay = getLastDay();
		days.push({
			x: (lastDay.x + 1) % 7, //
			y: Math.ceil((days.length + 1) / 7), //
			key: `${key_}-${i}`, // 2024-01-01
			day: i, // 日期
			type: 'next',
		});
	}
}
setTimeout(() => {
	updateDate(0);
}, 1000);
</script>
<style lang="scss" scoped>
.t-box {
	width: 420px;
	display: flex;
	flex-wrap: wrap;
	background-color: rgb(226, 230, 236);
	.t-li {
		width: 60px;
		text-align: center;
		border: 1px solid red;
		box-sizing: border-box;
	}
}
</style>
