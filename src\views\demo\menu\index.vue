<template>
	<div>
		<h1>菜单导航</h1>
		<h3>隐藏的菜单</h3>
		<h4>隐藏的菜单只会在顶部显示，不会在左侧菜单栏中显示</h4>
		<el-button @click="onClick">跳转到隐藏的菜单</el-button>
		{{ aaa }}
		<h3>新窗口</h3>
		<el-button @click="onClick2">打开新窗口</el-button>
		<el-input v-model="inputVal"></el-input>

		<el-button type="primary" @click="onClick3">首页</el-button>
	</div>
	<!-- <h3>菜单刷新</h3> -->
	<!-- <h4>每次打开新页面时，系统会缓存旧的页面的数据，点击顶部导航切换页面时不会刷新页面数据，点击左侧导航切换页面时会刷新页面数据</h4> -->
</template>

<script lang="ts" setup>
defineOptions({
	name: 'demo_menu',
});
import { rootOrigin } from '@/config';
import { routerInstance } from '@/router';
import { ref } from 'vue';
const inputVal = ref('');
const aaa = Math.random();
function onClick() {
	routerInstance.push({
		name: 'demo_hide',
	});
}
function onClick2() {
	window.open(`${rootOrigin}/demo/hide-menu`);
}
function onClick3() {
	routerInstance.push({
		name: 'demo_hide',
	});
}
</script>
<style lang="scss" scoped></style>
