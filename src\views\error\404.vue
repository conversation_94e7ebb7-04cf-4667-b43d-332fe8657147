<template>
	<div class="t-div" ref="refDom">
		<img src="@/assets/images/404.png" alt="" />
	</div>
</template>
<script lang="ts" setup>
import { isDev } from '@/config';
import { onMounted, ref } from 'vue';

const refDom = ref<HTMLDivElement>();
onMounted(() => {
	// 有时候页面会先跳转到404，然后瞬间跳走，这里加个延迟，不让页面显示。
	setTimeout(() => {
		refDom.value!.style.opacity = '1';
	}, 500);
});
</script>
<style scoped>
.t-div {
	text-align: center;
	padding-top: 100px;
	font-size: 30px;
	background-color: #fff;
	min-height: var(--root-vh100);
	box-sizing: border-box;

	opacity: 0;
	transition: all 0.5s;
}
</style>
