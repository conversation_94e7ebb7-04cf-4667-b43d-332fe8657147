import http, { resolveFunc, rejectFunc, EnumAim, XMLHttpDownload2, EnumContentType } from '@/utils/axios';
import * as Types from './types';
import { isDev } from '@/config';
const aim = EnumAim.test;

export enum Api {
	getList = '/web-api/weather/v1/getAlarm', //  分页查询表格数据
}
export function api_getList(param: Types.getListRequestType): Promise<Types.PageResultJobBaseVO> {
	const url = Api.getList;
	return http.get(url, param, { aim, loading: true }).then((res) => res[0] || {});
}
