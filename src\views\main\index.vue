<template>
	<el-container direction="vertical" class="t-layout">
		<PageHeader v-model:collapsed="collapsed" />
		<el-container class="t-layout-main">
			<el-aside v-model:collapsed="collapsed" :trigger="null" collapsible class="layout-sider">
				<ISideMenu :collapsed="collapsed" />
			</el-aside>
			<el-main class="t-layout-content">
				<NavBar></NavBar>
				<div class="t-big-content">
					<div
						ref="refContentBox"
						class="t-big-content-box"
						:class="{
							'is-container': isContainer,
						}"
					>
						<!-- <router-view v-slot="{ Component }">
							<keep-alive :include="['demo_menu']">
								<component :is="Component" />
							</keep-alive>
						</router-view> -->
						<router-view :key="key" />
					</div>
				</div>
			</el-main>
		</el-container>
	</el-container>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import ISideMenu from './menu/index.vue';
import PageHeader from './header/index.vue';
import NavBar from './menu/nav-bar.vue';
import { storeCommon } from '@/store';
import { useRoute, useRouter } from 'vue-router';
import { watch } from 'vue';
import { nextTick } from 'vue';

const collapsed = ref(false); // 左侧导航栏闭合状态

const currentRoute = useRoute(); // 当前路由
const router = useRouter(); // 当前路由
interface Props {}
const props = withDefaults(defineProps<Props>(), {});

const key = computed(() => {
	return currentRoute.fullPath + storeCommon.refresh + '';
});
const refContentBox = ref<HTMLDivElement>();
const isContainer = ref(false);
watch(
	key,
	(val) => {
		nextTick(() => {
			if (!refContentBox.value) return;
			// 如果子路由中没有调用 container-lcr 组件 添加一个类控制样式 这么做是为了减少dom层级
			isContainer.value = !refContentBox.value.getElementsByClassName('i-container-lcr-box').length;
		});
	},
	{
		immediate: true,
	},
);
</script>

<style lang="scss" scoped>
.t-layout {
	overflow: hidden;
	height: 100vh;
	.t-layout-main {
		flex-grow: 1;
		flex-shrink: 1;
		overflow: hidden;
	}
	.layout-sider {
		width: 208px;
		box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
		background-color: #fff;
		z-index: 1;
	}
	.t-layout-content {
		border-radius: 2px;
		display: flex;
		flex-direction: column;
		padding: 0;
		.t-big-content {
			flex-grow: 1;
			// background-color: #fff;
			overflow: auto;
			padding: 16px 0px 16px 16px;
		}
		.t-big-content-box {
			min-width: 1180px;
			width: 100%;
			height: 100%;
			box-sizing: border-box;
			overflow: hidden;
			position: relative;
			&.is-container {
				padding-right: 16px;
				overflow: auto;
			}
		}
	}
}
</style>
