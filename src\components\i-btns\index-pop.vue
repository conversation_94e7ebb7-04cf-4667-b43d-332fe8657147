<template>
	<div class="i-btns-box">
		<template v-for="btn in btns_">
			<el-popconfirm
				v-if="getTipsText(btn, data)"
				:title="getTipsText(btn, data)"
				:placement="btn.placement || placement"
				:disabled="checkDisabled(btn.disabled, data)"
				@confirm="
					(ev) => {
						btn.onClick(data, ev);
					}
				"
			>
				<template #reference>
					<el-button
						class="t-btn"
						:type="btn.type || btnType"
						:link="link"
						@click.prevent.stop
						:disabled="checkDisabled(btn.disabled, data)"
					>
						<icon-font v-if="btn.icon" :type="btn.icon" :title="btn.label"></icon-font>
						<template v-else>{{ btn.label }} </template>
					</el-button>
				</template>
			</el-popconfirm>
			<el-button
				v-else
				:link="link"
				:type="btn.type || btnType"
				:disabled="checkDisabled(btn.disabled, data)"
				class="t-btn"
				@click.stop="
					(ev) => {
						btn.onClick(data, ev);
					}
				"
			>
				<icon-font v-if="btn.icon" :type="btn.icon" :title="btn.label"></icon-font>
				<template v-else>{{ btn.label }} </template>
			</el-button>
		</template>
		<el-dropdown placement="bottom-end" v-if="btns_more.length">
			<el-button class="t-btn" link type="primary" @click.prevent.stop>
				<el-icon><More /></el-icon>
			</el-button>
			<template #dropdown>
				<div class="t-overlay-box">
					<template v-for="btn in btns_more">
						<div v-if="checkDisabled(btn.disabled, data)" class="t-overlay-item t-disabled">
							<icon-font v-if="btn.icon" :type="btn.icon" :title="btn.label"></icon-font>
							<template v-else>{{ btn.label }} </template>
						</div>
						<el-popconfirm
							v-else-if="getTipsText(btn, data)"
							:title="getTipsText(btn, data)"
							:placement="btn.placement || placement"
							@confirm="
								(ev) => {
									btn.onClick(data, ev);
								}
							"
						>
							<template #reference>
								<div class="t-overlay-item">
									<icon-font v-if="btn.icon" :type="btn.icon" :title="btn.label"></icon-font>
									<template v-else>{{ btn.label }} </template>
								</div>
							</template>
						</el-popconfirm>
						<div
							v-else
							class="t-overlay-item"
							@click.stop="
								(ev) => {
									btn.onClick(data, ev);
								}
							"
						>
							<icon-font v-if="btn.icon" :type="btn.icon" :title="btn.label"></icon-font>
							<template v-else>{{ btn.label }} </template>
						</div>
					</template>
				</div>
			</template>
		</el-dropdown>
	</div>
</template>

<script lang="ts" setup>
import { PropType, ref, watch } from 'vue';
import { ElButtonType, ElPopoverPlacement } from './types';
import { More } from '@element-plus/icons-vue';
import { getAuth } from '@/utils';

const props = defineProps({
	btns: {
		type: Array as PropType<Array<OptionBtn>>,
	},
	data: {
		//其它数据，会传回 点击事件中
		default: () => ({}),
	},
	btnType: {
		type: String as PropType<ElButtonType>,
		default: 'primary',
	},
	link: {
		type: Boolean as PropType<boolean>,
		default: true,
	},
	placement: {
		type: String as PropType<ElPopoverPlacement>,
		default: 'top-end',
	},

	//禁用
	disabled: {
		type: Boolean as PropType<boolean>,
		default: false,
	},
	moreNumber: {
		// 按钮超过多少个后，多出的按钮放到 ... 中
		type: Number as PropType<number>,
		// default: 3,
	},
});

const btns_ = ref<OptionBtn[]>([]);
const btns_more = ref<OptionBtn[]>([]);
function sort(a: OptionBtn, b: OptionBtn) {
	const order_a = a.order || Infinity;
	const order_b = b.order || Infinity;
	return order_a - order_b;
}

watch(
	() => props.btns,
	(val) => {
		if (val && val.length) {
			const list1: OptionBtn[] = [];
			const list2: OptionBtn[] = [];
			let isSort = false; // 是否需要排序
			val.forEach((item) => {
				if (getAuth(item.auth) && !checkHide(item.hide, props.data)) {
					isSort = isSort || !!item.order;
					if (item.more) {
						list2.push(item);
					} else {
						list1.push(item);
					}
				}
			});
			if (isSort) {
				list1.sort(sort);
				list2.sort(sort);
			}
			if (list1.length && props.moreNumber) {
				const length_ = list1.length - props.moreNumber;
				for (let i = 0; i < length_; i++) {
					list2.unshift(list1.pop()!);
				}
			}
			btns_.value = list1;
			btns_more.value = list2;
		} else {
			btns_.value = [];
			btns_more.value = [];
		}
	},
	{
		immediate: true,
		deep: true,
	},
);
function checkHide(hide: OptionBtn['hide'], row: any) {
	if (typeof hide === 'boolean') return hide;
	if (typeof hide === 'function') return hide(row);
	return false;
}
function checkDisabled(disabled: OptionBtn['disabled'], row: any) {
	if (props.disabled) return props.disabled;
	if (typeof disabled === 'boolean') return disabled;
	if (typeof disabled === 'function') return disabled(row);
	return false;
}
function getTipsText(btn: OptionBtn, row: any) {
	if (!btn.tips) return '';
	if (typeof btn.tips === 'string') return btn.tips;
	let tips_: string | boolean = false;
	if (typeof btn.tips === 'boolean') {
		tips_ = btn.tips;
	} else if (typeof btn.tips === 'function') {
		tips_ = btn.tips(row);
	}
	return typeof tips_ === 'string' ? tips_ : '确定吗？';
}
</script>
<style lang="scss" scoped>
.i-btns-box {
	display: inline-flex;
	flex-wrap: wrap;
	> :deep(.el-dropdown) {
		color: var(--el-button-text-color);
		margin-right: 8px;
	}
	> :deep(.t-btn) {
		margin-left: 0;
		margin-right: 8px;
	}
	> *:last-child {
		margin-right: 0;
	}
}
.t-overlay-box {
	position: relative;
	margin: 0;
	padding: 4px 0;
	text-align: left;
	list-style-type: none;
	background-color: #fff;
	background-clip: padding-box;
	border-radius: 2px;
	outline: none;
	box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
	.t-overlay-item {
		margin: 0;
		padding: 5px 22px;
		color: #000000d9;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		cursor: pointer;
		transition: all 0.3s;
		text-align: center;
		&:hover {
			background-color: #f5f5f5;
		}
		&.t-disabled {
			cursor: not-allowed;
			color: var(--el-disabled-text-color);
		}
	}
}
</style>
