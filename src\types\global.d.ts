interface Loading {
	value: boolean;
}

declare type FormRules<T = unknown> = Partial<Record<keyof T, any>>;
declare const G_loading: Loading;
declare interface Window {
	G_loading: Loading;
}

declare interface EnvConfig {
	documentTitle: string; // 平台名称
	VITE_BASE_API: string; // 接口前缀 "http://************/jyjc-service",
	VITE_APP_NAME: string; // "jyjc-pre-web",
	VITE_API_MENU_LIST: string; // '/web/portal/menu/list'
	VITE_CAS_AUTH_URL: string; // "http://************/iip-tenant-cas-client",
	VITE_CAS_LOGOUT_URL: string; // "http://************/iip-tenant-sso/logout",
	VITE_IDAAS_API: string; //
	VITE_client_id: string;
	VITE_client_secret: string;
	VITE_BASE_FILE_API: string; // "http://************:8811/basic-service",
	VITE_BASE_FILE_SATIC_PATH: string; // "http://************:8811/wscp-file"
}
declare interface NODE_ENV {
	BASE_URL: '/' | './'; // "/"
	NODE_ENV: 'development' | 'production'; // "development"
}

declare type Nullable<T> = T | null;
