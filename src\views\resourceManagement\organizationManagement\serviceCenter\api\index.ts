import http, { resolveFunc, rejectFunc, EnumAim, XMLHttpDownload2, EnumContentType } from '@/utils/axios';
import * as Types from './types';
import { isDev } from '@/config';
const aim = EnumAim.test;

export enum Api {
	getList = '/web-api/service-center/v1/center/page', //  分页查询表格数据
	saveCenter = '/web-api/service-center/v1/center/save', //  保存服务中心
	editCenter = '/web-api/service-center/v1/center/modify', //  修改服务中心
	deleteCenter = '/web-api/service-center/v1/center/', //  删除服务中心
	importCenter = '/web-api/service-center/v1/center/import', //  导入服务中心
	getUserList = '/web-api/service-user/v1/center/page/filter', //  分页查询人员信息表格数据
	getEquipmentList = '/web-api/equipment/v1/center/page', //  分页查询机具信息表格数据
}
export function api_getList(param: Types.getListRequestType): Promise<Types.PageResultServiceCenterBaseVO> {
	const url = Api.getList;
	return http.get(url, param, { aim, loading: true }).then((res) => res[0] || {});
}

export function api_saveCenter(param: Types.RequestSaveCenter): Promise<any> {
	const url = Api.saveCenter;
	return http.post(url, param, { aim, successTips: true, loading: true });
}

export function api_editCenter(param: Types.RequestSaveCenter): Promise<any> {
	const url = Api.editCenter;
	return http.post(url, param, { aim, successTips: true, loading: true });
}

export function api_delete(serviceUuid: string) {
	const url = Api.deleteCenter + serviceUuid;
	return http.delete(url, null, { aim, successTips: false, loading: true });
}

export function api_import(file: any, tenantId: string) {
	const url = Api.importCenter;
	return http.post(
		url,
		{ file: file },
		{ aim, query: { tenantId }, contentType: EnumContentType.FORM_DATA, successTips: true, loading: true, toFormData: true },
	);
}
export function api_getUserList(param: Types.getListRequestType): Promise<Types.PageResultCenterUserBaseVO> {
	const url = Api.getUserList;
	return http.get(url, param, { aim, loading: true }).then((res) => res[0] || {});
}
export function api_getEquipmentList(param: Types.getListRequestType): Promise<Types.PageResultEquipmentBaseDTO> {
	const url = Api.getEquipmentList;
	return http.get(url, param, { aim, loading: true }).then((res) => res[0] || {});
}
