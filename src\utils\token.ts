import { isDev, tokenTimeout } from '@/config';
import { setStoreCommon } from '@/store';

// token方法没有使用 storage
enum EnumLogin {
	toKenKey = 'EnumLogin_OILKAJVOIASHEUYNVLKASHD', //  localStorage 中 token的key
	isLogIn = 'EnumLogin_GLAKSFBVUIY34329SAKJ', // 是否登录
	newestTime = 'EnumLogin_LKJ9G828LKJ2L35J2LJ9S8', // 更新活动时间
	true = 'EnumLogin_7345gksd095sgt', // 表示true
	false = 'EnumLogin_734hsi987s4523', // 表示false
}
function getAppToken() {
	// 获取最新token 暂未开发；
	return Promise.resolve('5AuCxPREFv');
}

// if (isDev) {
// 	setTimeout(() => {
// 		// 设计单位 token  65963e012b5e7d71dccfd149
// 		// 审图结构 token  65963e4b2b5e7d71dccfd14b
// 		// 建设单位 token  65961cba2b5e7d71dccfd147
// 		// setTokenLocal('6597c7662b5e7d699c7748f3');
//     // setTokenLocal('659df3e41bfc6368ecf4a136');
// 		// setTokenLocal('65961cba2b5e7d71dccfd147');
// 	}, 300);
// }
/**
 * 获取token值
 */
export function getTokenPromise(): Promise<string> {
	let token = getLocalToken();
	if (token) {
		return Promise.resolve(token);
	} else {
		return Promise.reject('token过期');
		return getAppToken().then((token) => {
			setTokenLocal(token);
			return token;
		});
	}
}

let timeStr = new Date().getTime();
let token_ = '';
/**
 * 获取token值
 */
export function getLocalToken(): string | null {
	// return 'sdlkfjfjw9oi023952ol3ij42o3ut89';
	if (!token_) {
		localStorage.removeItem('local_casUserToken');
	}
	return token_;
	// timeStr = Number(localStorage.getItem(EnumLogin.newestTime)); // 获取时间 超过时间的话，就清除 token
	// let token: string = '';
	// if (timeStr) {
	// 	const oldTime = timeStr;
	// 	const newTime = new Date().getTime();
	// 	if (newTime - oldTime > 1000 * 60 * 60 * tokenTimeout) {
	// 	} else {
	// 		// 没有超时时 刷新时间戳
	// 		timeStr = new Date().getTime();
	// 		localStorage.setItem(EnumLogin.newestTime, new Date().getTime() + ''); // 保存当前时间轴，每次获取token时刷新一次
	// 		token = localStorage.getItem(EnumLogin.toKenKey) || '';
	// 	}
	// }
	// if (token) {
	// 	return token;
	// } else {
	// 	setTokenLocal();
	// 	return null;
	// }
}

/**
 * 设置token的值
 */
export function setTokenLocal(token?: string) {
	setStoreCommon({
		isLogined: !!token,
	});
	token_ = token || '';
	if (!token_) {
		localStorage.removeItem('local_casUserToken');
	}
	// if (token) {
	// 	localStorage.setItem(EnumLogin.toKenKey, token);
	// 	localStorage.setItem(EnumLogin.newestTime, new Date().getTime() + '');
	// } else {
	// 	// 没有 token 表示退出登录
	// 	localStorage.removeItem(EnumLogin.newestTime);
	// }
}
