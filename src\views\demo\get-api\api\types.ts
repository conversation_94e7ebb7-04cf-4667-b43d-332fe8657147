// 查看 详情
export interface ResponseInfo {
	approveResult: string; //   * 审核结果备注
	approveStatus: number; // * 审核状态，[-2]审核退回;[0]待提交;[5]审核中;[10]审核通过
	assessmentDate: string; // * 评估时间
	countryCode: string; // * 国别编码
	countryName: string; // * 国别名称
	createTime: string; // * 创建时间
	createUserId: string; // * 创建人id
	currentFlag: number; // * 当前最新数据标识
	dataApproveDesc: string; // * 数据审批操作描述
	dataSource: number; // * 数据来源
	dataValidFlag: number; // * 0:非有效数据；1:有效数据;-1:待删除的有效数据
	enterpriseId: string; // * 所属单位编码
	enterpriseName: string; // * 所属单位名称
	filingResult: string; // * 备案结果
	filingStatus: number; // * 备案状态
	filingTime: string; // * 备案时间
	gzwReportDate: string; // * 国资委下发报期
	id: string; // * 牵头企业国别安全形势分析记录唯一编号
	isCurrent: number; // * 是否最新，基于国别+分析时间
	issueNumber: string; // * 期号
	parent_report_id: string; // * 所属机构/项目/事项的上报id
	reportId: string; // * 上报唯一编号
	reportName: string; // * 报告名称
	riskLevel: string; // * 风险等级
	userDataFlag: string; // * 用户操作数据标识
}
