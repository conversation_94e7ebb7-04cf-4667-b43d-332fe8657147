<template>
	<h3>分页 通过v-model双向绑定更新页码 当前页码：{{ pageInfo.pageNumber }}</h3>
	<common-table
		:columns="columns"
		:data="data"
		:total="pageInfo.total"
		v-model:pageNumber="pageInfo.pageNumber"
		:fill-screen="false"
		:pagination="true"
	></common-table>
	<h3>分页 通过current-change事件更新页码 当前页码：{{ pageInfo.pageNumber }}</h3>
	<common-table
		:columns="columns"
		:data="data"
		v-bind="pageInfo"
		:pagination="true"
		:fill-screen="false"
		@page-current-change="onPageCurrentChange"
	></common-table>
	<h3>分页 使用pagination设置多个参数</h3>
	<common-table :columns="columns" :data="data" :fill-screen="false" :pagination="pagination"></common-table>
</template>

<script lang="tsx" setup>
import { reactive } from 'vue';
const pagination = reactive<ElPaginationProps>({
	small: true,
	layout: 'prev, pager, next, jumper, ->, total',
	background: true,
	total: 200,
});
const pageInfo = reactive<PageInfo>({
	total: 200,
	pageNumber: 1,
	pageSize: 20,
});
function onPageCurrentChange(val: number) {
	// getList()
	pageInfo.pageNumber = val;
	console.log('1111111111 onPageCurrentChange', val);
}

interface DataItem {
	key1: string;
	key2: string;
}

const data: DataItem[] = [
	{
		key1: 'sldkfjl',
		key2: 'sldkfjl',
	},
	{
		key1: 'sldkfjl3245',
		key2: 'sldkfjl2345',
	},
	{
		key1: 'sldkfjl2345',
		key2: 'sldkfjl2345',
	},
];
const columns: CommonTableColumn<DataItem>[] = [
	{
		label: 'label1',
		prop: 'key1',
	},
	{
		label: 'label2',
		prop: 'key2',
	},
];
</script>
<style lang="scss" scoped></style>
