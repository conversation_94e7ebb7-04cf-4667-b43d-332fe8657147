<template>
	<h1>数据字典</h1>
	<h3>通过调用接口获取字典</h3>
	<el-select placeholder="请选择建筑性质">
		<el-option v-for="item in BuildNature" :label="item.name" :value="item.code" />
	</el-select>
	<h3>通过 useDicts 获取字典</h3>
	<el-select placeholder="请选择建筑性质">
		<el-option v-for="item in EngineeringType" :label="item.name" :value="item.code" />
	</el-select>
</template>

<script lang="ts" setup>
import { api_getDicts } from '@/api/dict';
import useDicts from '@/store/use-dicts';
import { ref } from 'vue';

const BuildNature = ref<DicDataItem[]>([]);
const arr1 = [
	'BuildNature', //建筑性质
	'SurveyLevel', // SurveyLevel
];
api_getDicts(arr1).then((maps) => {
	BuildNature.value = maps.BuildNature;
});
const arr2 = [
	'EngineeringType', // 工程类型 ,
	'constructScope', // 建筑规模
];
const { EngineeringType, constructScope } = useDicts(arr2);
</script>
<style lang="scss" scoped></style>
