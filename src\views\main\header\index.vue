<template>
	<el-header class="layout-header">
		<div class="left-options">
			<img src="@/assets/images/header_name.png" alt="logo" class="logo-box" title="回到首页" />
		</div>
		<div class="header-menu-box">
			<div
				class="item"
				:class="{ active: storeSelectFirstMenu?.name == item.name }"
				@click="onSelect(item as RouteRecordRaw)"
				v-for="(item, index) in storeMenus"
			>
				{{ item?.meta?.title || defaultText }}
			</div>
			<div class="right-options">
				<el-dropdown @command="onCommand">
					<div class="t-user">
						{{ storeUserInfo.name }} <el-icon><CaretBottom /></el-icon>
					</div>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item command="userInfo">用户中心</el-dropdown-item>
							<el-dropdown-item command="password">修改密码</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<icon-font class="t-logout" type="icon-logout" title="退出登录" @click="onlogout"></icon-font>
				<DialogPassword v-model="dialogVisible"></DialogPassword>
				<DialogUserinfo v-model="dialogVisibleUserInfo"></DialogUserinfo>
			</div>
		</div>
	</el-header>
</template>

<script lang="ts" setup>
import { goToLogout } from '@/api/user';
import { routerInstance } from '@/router';
import { storeUserInfo } from '@/store';
import { commonConfirm } from '@/utils';
import { CaretBottom } from '@element-plus/icons-vue';
import DialogPassword from './dialog-password.vue';
import DialogUserinfo from './dialog-userinfo.vue';
import { RouteRecordRaw, useRoute } from 'vue-router';
import { PropType, ref, watch } from 'vue';
import { setStoreCommon, storeCommon, storeMenus, storeSelectFirstMenu } from '@/store';
import { defaultText } from '@/config';
interface Props {
	collapsed: boolean;
}
const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits(['update:collapsed']);
const dialogVisible = ref(false);
const dialogVisibleUserInfo = ref(false);
function goToIndex() {
	routerInstance.push({
		name: 'main',
	});
}
function onlogout() {
	commonConfirm('是否退出？').then(() => {
		goToLogout();
	});
}
function onCommand(name: string) {
	if (name === 'password') {
		dialogVisible.value = true;
	} else if ((name = 'userInfo')) {
		dialogVisibleUserInfo.value = true;
	}
}

const onSelect = (root: RouteRecordRaw) => {
	console.log(storeSelectFirstMenu);
	routerInstance.push({
		name: root.name,
	});
};
</script>

<style lang="scss" scoped>
.layout-header {
	display: flex;
	// justify-content: space-between;
	// align-items: center;
	width: 100%;
	padding: 0;
	flex-shrink: 0;
	position: relative;
	min-width: 1400px;
	// background-color: rgba(212, 91, 91, 1);
	background-image: url('@/assets/images/header_banner.png');
	background-size: 100% 100%;
	background-position: center;
	background-repeat: no-repeat;
	height: 154px;
	z-index: 2;
	// box-shadow: 0px 4px 10px 0px rgba(78, 89, 105, 0.06), 0px 4px 10px 0px rgba(78, 89, 105, 0.06);

	.left-options {
		flex-shrink: 0;
		height: 100%;
		display: flex;
		align-items: center;
		.logo-box {
			display: block;
			// max-height: 30px;
			margin-left: 16px;
			height: 24px;
			width: 426px;
			// cursor: pointer;
		}
		.t-text {
			font-size: 20px;
			margin-left: 10px;
			margin-top: 10px;
			font-weight: 700;
			color: rgb(215, 190, 57);
		}
	}
	.t-text-center {
		position: absolute;
		width: 100%;
		height: 154px;
		line-height: 154px;
		text-align: center;
		font-weight: 700;
		font-style: normal;
		font-size: 54px;
		letter-spacing: 14px;
		color: rgb(215, 190, 57);
		height: 154px;
		line-height: 104px;
	}

	.header-menu-box {
		position: absolute;
		z-index: 3;
		width: 100%;
		bottom: 0;
		padding-left: 20px;
		background-color: rgba(34, 232, 99, 0.29);
		height: 42px;
		display: flex;
		align-items: center;

		.item {
			width: 180px;
			font-size: 16px;
			box-sizing: border-box;
			font-weight: 600;
			color: #fff;
			text-align: center;
			height: 42px;
			line-height: 42px;
			cursor: pointer;
		}
		.active {
			background-color: rgba(240, 248, 254, 1);
			// border-top: 4px solid #c40001;
			border-radius: 3px 3px 0px 0px;
			color: rgba(19, 108, 77, 1);
		}
		.right-options {
			position: absolute;
			right: 30px;
			display: flex;
			align-items: end;
			color: #fff;
			padding-bottom: 0px;
			.t-user {
				font-size: 18px;
				color: #fff;
			}
		}
	}
}
.t-logout {
	margin-left: 20px;
	cursor: pointer;
}
</style>
<style lang="scss"></style>
