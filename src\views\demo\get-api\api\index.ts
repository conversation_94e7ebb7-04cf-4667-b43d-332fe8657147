import http, { resolveFunc, rejectFunc, EnumAim } from '@/utils/axios';
import * as Types from './types';
export * from './types';
const aim = EnumAim.test;

export enum Api {
	getList = '/web-api/qtqy/v1/country/list', //  测试地址11
}

export function api_getList(id: string): Promise<Types.ResponseInfo[]> {
	// if (isDev) return resolveFunc();
	const url = Api.getList;
	return http.get(
		url,
		{
			id,
		},
		{
			aim,
		},
	);
}
