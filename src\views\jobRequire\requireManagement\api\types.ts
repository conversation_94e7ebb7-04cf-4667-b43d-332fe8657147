export interface getListRequestType {
	key?: string;
	pageNo: number;
	pageSize: number;
	[property: string]: any;
}

/**
 * PageResultJobBaseVO
 */
export interface PageResultJobBaseVO {
	list: JobBaseVO[];
	total: number;
	[property: string]: any;
}

/**
 * JobBaseVO
 */
export interface JobBaseVO {
	addressDetail: string; //详细地址
	cityCode: string; //地市编码
	contactName: string; //联系人名称
	contactTelephone: string; //联系人手机号
	districtCode: string; //区县编码
	finishTime: string; //需求完成时间
	jobEquipments: JobEquipmentDTO[]; //作业设备
	jobArea: number; //作业面积
	jobInfo: string; //需求详情
	jobPublisherUuid: string; //发布人员
	jobStatus: number; //状态  0草稿  1.待响应 2.进行中 3.已完成
	jobType: string; //作业类型
	jobWorker: string; //接单人员
	latitude: string; //维度
	longitude: string; //经度
	provinceCode: string; //省份编码
	remark: string; //备注
	uuid: string; //唯一编码
	[property: string]: any;
}

/**
 * SaveJobDTO
 */
export interface RequestSave {
	uuid?: string;
	addressDetail: string; //详细地址
	cityCode: string; //地市编码
	contactName: string; //联系人名称
	contactTelephone: string; //联系人手机号
	districtCode: string; //区县编码
	finishTime: string; //需求完成时间
	jobArea: number | string; //作业面积
	jobEquipments: JobEquipmentDTO[]; //作业设备
	jobInfo: string; //需求详情
	jobType: string; //作业类型
	latitude: string; //维度
	longitude: string; //经度
	provinceCode: string; //省份编码
	remark: string; //备注
	[property: string]: any;
}

/**
 * JobEquipmentDTO
 */
export interface JobEquipmentDTO {
	count: number | string;
	equipmentCode: string;
	[property: string]: any;
}
