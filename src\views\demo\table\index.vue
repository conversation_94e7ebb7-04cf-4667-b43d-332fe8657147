<template>
	<h1>表格</h1>
	<h4>如果使用jsx语法，需要设置 script lang="tsx" setup</h4>
	<h4>本组件支持element-plus el-table 所有参数，可直接传入官方参数</h4>
	<h3>columns方式</h3>
	<common-table :columns="columns1" size="small" :fill-screen="false" :max-height="90" :data="data"></common-table>
	<h3>columns方式+插槽方式+快捷按钮组</h3>
	<common-table :columns="columns2" :data="data" :fill-screen="false" :getBtns="getBtns" :operateWidth="150">
		<template #slotName1="{ column, $index }">
			<span style="color: red">{{ column.label }} </span>
		</template>
		<template #slotName2="{ row, column, $index }">
			<span style="color: red"> {{ row.key2 }}</span>
		</template>
		<template #action="scope">
			<el-button link size="small" @click.stop.prevent="handleCommand('upload', scope.row)">上传图纸</el-button>
			<span>|</span>
			<el-button link size="small" @click.stop.prevent="handleCommand('view', scope.row)">查看图纸</el-button>
		</template>
	</common-table>
	<h3>官方示例</h3>
	<common-table :data="data" :fill-screen="false">
		<el-table-column prop="key1" label="label1">
			<template #header="{ column, $index }">
				<span style="color: red">{{ column.label }} </span>
			</template>
		</el-table-column>
		<el-table-column prop="key2" label="label2">
			<template #default="{ row, column, $index }">
				<span style="color: red"> {{ row.key2 }}</span>
			</template>
		</el-table-column>
		<el-table-column label="操作">
			<template #default="{ row, column, $index }">
				<i-btns :btns="btns" :more-number="1" :data="row"></i-btns>
			</template>
		</el-table-column>
	</common-table>
</template>

<script lang="tsx" setup>
interface DataItem {
	key1: string;
	key2: string;
}

const data: DataItem[] = [
	{
		key1: 'sldkfjl',
		key2: 'sldkfjl',
	},
	{
		key1: 'sldkfjl3245',
		key2: 'asdf asf asf asf sqf2 r2来看风景卢卡斯的积分拉萨开发三楼的看法十萨芬撒旦发生发生六分拉萨开发 ',
	},
	{
		key1: 'sldkfjl2345',
		key2: 'sldkfjl2345',
	},
];
const btns: OptionBtn<DataItem>[] = [
	{
		label: '查看',
		auth: '',
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '隐藏的按钮1',
		auth: '',
		hide: true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '隐藏的按钮2',
		auth: '',
		hide: () => true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '禁用按钮',
		auth: '',
		disabled: () => true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '删除',
		auth: '',
		tips: '确定删除吗？',
		placement: 'left-start',
		more: true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '编辑',
		auth: '',
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
];
function getBtns(row: DataItem, column: CommonTableColumn<DataItem>) {
	console.log('111111111111111111111getBtns ', row, column);
	return btns;
}
const columns1: CommonTableColumn<DataItem>[] = [
	{
		label: 'label1',
		prop: 'key1',
		//align: 'center',
		// 自定义header
		renderHeader: ({ column, $index }) => {
			return <span style="color:red">{column.label} </span>;
		},
	},
	{
		label: 'label2',
		prop: 'key2',
		// 自定义渲染内容
		formatter: (row, column, cellValue, index) => {
			return <span style="color:red">{cellValue}</span>;
		},
	},
	{
		label: '项目状态',
		prop: 'key2',
		width: 180,
		// 自定义渲染内容
		formatter: (row, column, cellValue, index) => {
			return cellValue;
		},
	},
	{
		label: '操作',
		//align: 'center',
		formatter: (row, column, cellValue, index) => {
			const btn_view: OptionBtn = {
				label: '查看',
				auth: '',
				onClick() {
					console.log('点击了按钮', row);
				},
			};
			const btn_del: OptionBtn = {
				label: '删除',
				auth: '',
				tips: '确定删除吗？',
				placement: 'left-start',
				more: true,
				onClick() {
					console.log('点击了按钮', row);
				},
			};
			const btn_edit: OptionBtn = {
				label: '编辑',
				auth: '',
				onClick() {
					console.log('点击了按钮', row);
				},
			};
			const btns: OptionBtn[] = [];
			// 通过当前数据的状态控制需要显示的按钮
			if (row.key1) {
				btns.push(btn_edit);
			}
			if (row.key2) {
				btns.push(btn_del);
			}
			btns.push(btn_view);
			return <i-btns btns={btns}></i-btns>;
		},
	},
];
const columns2: CommonTableColumn[] = [
	{
		label: 'label1',
		prop: 'key1',
		//align: 'center',
		slotHeaderName: 'slotName1', // 自定义header
	},
	{
		label: 'label2',
		prop: 'key2',
		slotName: 'slotName2', // 自定义渲染内容
	},
	{
		label: '操作',
		//align: 'center',
		slotName: 'action', // 自定义渲染内容
	},
];
function handleCommand(key: string, row: DataItem) {
	console.log('111111111111111111 点击了按钮', key, row);
}
</script>
<style lang="scss" scoped></style>
