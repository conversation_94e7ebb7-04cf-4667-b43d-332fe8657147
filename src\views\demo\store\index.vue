<template>
	<h1>全局状态管理</h1>
	<h3>
		pinia（vue3中推荐使用pinia，类似vuex）
		<a href="https://pinia.web3doc.top/introduction.html" target="_blank">pinia官网</a>
	</h3>
	<el-button @click="setTestInfo"> 设置testInfo </el-button>
	{{ testStore.getTestInfo }}
	<h3>使用全局变量</h3>
	<h4>在@/store/index.ts中定义响应式变量并导出，可以在任何模块中使用，并触发响应式更新。</h4>
	<el-button @click="setUserInfo"> 设置userInfo </el-button>
	{{ storeUserInfo.name }}
</template>

<script lang="ts" setup>
import { setStoreUserInfo, storeUserInfo } from '@/store';
import { useTestStore } from '@/store/modules/test';
import { watch } from 'vue';
const testStore = useTestStore();

function setTestInfo() {
	testStore.setTestInfo({
		a: Math.random() + '3434',
		b: Math.random(),
	});
}
watch(
	() => testStore.getTestInfo,
	(val) => {
		console.log('111111111111 testStore.getTestInfo', testStore.getTestInfo);
	},
);

function setUserInfo() {
	setStoreUserInfo({
		name: '用户名' + Math.random(),
	});
}
watch(
	() => storeUserInfo.name,
	(val) => {
		console.log('111111111111 storeUserInfo.name', storeUserInfo.name);
	},
);
</script>
<style lang="scss" scoped></style>
