<template>
	<el-dialog
		v-model="dialogVisible"
		width="550px"
		:title="title"
		:close-on-click-modal="false"
		append-to-body
		align-center
		destroy-on-close
		@closed="closeDialog"
	>
		<el-form ref="formRef" style="margin: 0 12px" :model="formData" :disabled="type == 'view'" label-width="80px" :rules="formRules">
			<el-row :gutter="24">
				<el-col>
					<el-form-item label="行政区划" prop="address">
						<el-cascader
							:props="{
								value: 'areaId',
								label: 'areaName',
								children: 'children',
							}"
							placeholder="请选择行政区划"
							clearable
							style="width: 100%"
							v-model="formData.address"
							:options="areas"
							@change="handleChange"
						/>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col>
					<el-form-item label="所属组织" prop="centerUuid">
						<el-select v-model="formData.centerUuid" placeholder="请选择所属服务中心">
							<el-option
								v-for="item in centerListOptions"
								:key="item.centerUuid"
								:label="item.centerName"
								:value="item.centerUuid"
							/>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col>
					<el-form-item label="姓名" prop="userName">
						<el-input v-model="formData.userName" placeholder="请输入人员姓名"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col>
					<el-form-item label="联系方式" prop="userTelephone">
						<el-input placeholder="请输入联系方式" v-model="formData.userTelephone"></el-input>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row :gutter="24">
				<el-col>
					<el-form-item label="备注" prop="remark">
						<el-input v-model="formData.remark" placeholder="请输入备注信息" type="textarea" :rows="2" resize="none" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<el-row justify="center">
			<el-button @click="closeDialog" style="margin-right: 12px">取消</el-button>
			<el-button type="primary" @click="submit" v-if="type !== 'view'" style="margin-right: 12px">确认</el-button>
		</el-row>
	</el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ElForm, FormRules } from 'element-plus';
import { RequestSaveCenter, CenterUserBaseVO, CenterList } from '../api/types';
import { api_editCenter, api_getCenterList, api_saveCenter } from '../api';
import { watch } from 'vue';
import { setObjValue } from '@/utils';
import { api_getArea } from '@/api/dict';
import { storeUserInfo } from '@/store';
interface Props {
	type: string; //新增还是查看还是编辑
	rowData?: CenterUserBaseVO | null;
}
const props = withDefaults(defineProps<Props>(), {
	type: 'add',
});
const dialogVisible = defineModel<boolean>('visible');

const title = computed(() => {
	switch (props.type) {
		case 'add':
			return '新增';
		case 'view':
			return '查看';
		case 'edit':
			return '编辑';
		default:
			break;
	}
});
const emits = defineEmits<{
	(ev: 'saveForm'): void;
}>();

const centerListOptions = ref<CenterList[]>([]);
onMounted(() => {
	api_getArea().then((res) => {
		console.log(res, '8888');
		areas.value = res;
	});
	fetchCenterList();
});
function fetchCenterList() {
	api_getCenterList({
		provinceCode: formData.value.address ? formData.value.address[0] : '',
		cityCode: formData.value.address ? (formData.value.address[1] ? formData.value.address[1] : '') : '',
		districtCode: formData.value.address ? (formData.value.address[2] ? formData.value.address[2] : '') : '',
		tenantId: storeUserInfo.enterpriseId,
	}).then((res) => {
		centerListOptions.value = res;
	});
}
const defaultData: RequestSaveCenter = {
	centerUuid: '', //中心uuid
	remark: '', //备注
	userName: '', //    用户名
	userTelephone: '', //    用户手机号
	address: [],
};

const formData = ref<RequestSaveCenter>(Object.assign({}, defaultData, { subsidyRule: [] }));
const rules: FormRules<RequestSaveCenter> = Object.keys(formData.value).reduce((acc, key) => {
	acc[key] = [
		{
			required: true,
			message: '不能为空',
			trigger: 'blur',
		},
	];
	return acc;
}, {} as FormRules);

const formRules = ref<FormRules<RequestSaveCenter>>(rules);
delete formRules.value.address;
delete formRules.value.remark;
const formRef = ref<InstanceType<typeof ElForm>>();
watch(
	() => dialogVisible,
	(newValue, oldValue) => {
		setObjValue(formData.value, {}, defaultData);
		if (!newValue || props.type == 'add') return;
		setObjValue(formData.value, props.rowData!, defaultData);
		formData.value.userName = props.rowData?.sysUserName;
		formData.value.centerUuid = props.rowData?.centerId;
		formData.value.address = [props.rowData?.provinceCode, props.rowData?.cityCode];
		if (props.rowData?.districtCode) formData.value.address.push(props.rowData?.districtCode);
	},
	{ immediate: true, deep: true },
);
function submit() {
	formRef.value?.validate((valid) => {
		if (!valid) return;
		let param = {
			centerUuid: formData.value.centerUuid, //中心uuid
			remark: formData.value.remark, //备注
			userName: formData.value.userName, //    用户名
			userTelephone: formData.value.userTelephone,
		};
		let api_ = props.type == 'add' ? api_saveCenter : api_editCenter;
		if (props.type == 'edit') param['uuid'] = props.rowData!.uuid;

		api_(param).then((res) => {
			emits('saveForm');
			dialogVisible.value = false;
		});
	});
}

function closeDialog() {
	setObjValue(formData.value, {}, defaultData);
	dialogVisible.value = false;
	formRef.value!.clearValidate();
}

defineExpose({
	open,
	close,
});

const handleChange = (value) => {
	console.log(value);
	fetchCenterList();
};

const areas = ref<AreaTreeBase[]>([]);
</script>
<style lang="scss" scoped>
.add-btn-t {
	width: 100%;
	padding: 12px 0;
	display: flex;
	justify-content: center;
	border-radius: 12px;
	background-color: #ecf5ff;
	margin-bottom: 12px;
	// border: 1px solid #f2f3f5;

	cursor: pointer;
	// color: #ecf5ff;
	// border: 1px solid #ecf5ff;
}
</style>
