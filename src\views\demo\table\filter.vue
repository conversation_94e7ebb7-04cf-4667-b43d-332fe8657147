<template>
	<el-button @click="resetDateFilter">reset date filter</el-button>
	<el-button @click="clearFilter">reset all filters</el-button>
	<common-table
		:columns="columns4"
		ref="refTable"
		:fill-screen="false"
		row-key="date"
		:get-list="getList"
		@sort-change="onSortChange"
		@filter-change="onFilterChange"
		:sort-method="sortMethod"
		:data="tableData"
	></common-table>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { TableColumnCtx } from 'element-plus';

interface User {
	date: string;
	name: string;
	address: string;
	tag: string;
}
const refTable = ref<CommonTableInstance>();
const resetDateFilter = () => {
	refTable.value?.handle('clearFilter', ['date']);
};
const clearFilter = () => {
	refTable.value?.handle('clearFilter');
};
const filterTag = (value: string, row: User) => {
	return row.tag === value;
};
const filterHandler = (value: string, row: User, column: TableColumnCtx<User>) => {
	const property = column['property'];
	return row[property] === value;
};
function getList(aa) {
	console.log('111111111111111111', aa);
	return Promise.reject();
}
const columns4: CommonTableColumn<User>[] = [
	{
		label: 'Date',
		prop: 'date',
		width: 180,
		sortable: true,
		columnKey: 'date', // 筛选时需要这个key
		filterMethod: filterHandler,
		// filteredValue: ['2016-05-01'],
		filters: [
			{ text: '2016-05-01', value: '2016-05-01' },
			{ text: '2016-05-02', value: '2016-05-02' },
			{ text: '2016-05-03', value: '2016-05-03' },
			{ text: '2016-05-04', value: '2016-05-04' },
		],
	},
	{
		label: 'Name',
		width: 180,
		prop: 'name',
	},
	{
		label: 'Address',
		prop: 'address',
		searchable: 'custom',
		searchKey: 'address2',
	},
	{
		label: 'Tag',
		prop: 'tag',
		columnKey: 'tag', // 筛选时需要这个key
		width: 100,
		sortable: 'custom', // true,
		filterMethod: filterTag,
		filters: [
			{ text: 'Home', value: 'Home' },
			{ text: 'Office', value: 'Office' },
		],
	},
];
const tableData: User[] = [
	{
		date: '2016-05-03',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
		tag: 'Home',
	},
	{
		date: '2016-05-02',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
		tag: 'Office',
	},
	{
		date: '2016-05-04',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
		tag: 'Home',
	},
	{
		date: '2016-05-01',
		name: 'Tom',
		address: 'No. 189, Grove St, Los Angeles',
		tag: 'Office',
	},
];
function onSortChange(data: { column: any; prop: string; order: any }) {
	console.log('onSortChange', data);
}
function onFilterChange(newFilters: any) {
	console.log('onFilterChange', newFilters);
	console.log('onFilterChange111111111111', refTable.value?.handle('getFilters'));
}
function sortMethod(a, b) {
	console.log('sortMethod', a, b);
	return 1;
}
</script>
