import axios from 'axios';
import type { AxiosRequestConfig, AxiosInstance, AxiosResponse } from 'axios';
import { isArray, isObject, isString } from '@vue/shared';
import { cloneDeep } from 'lodash-es';
export class IAxios<TEnumAim, ResultCodeCustom> {
	public axiosInstance: AxiosInstance;
	private optionsTrue: IAxiosOptionsTrue<TEnumAim>;
	private defaultOptions: IAxiosOptionsTrue<TEnumAim> = {
		config: {},
		headers: {},
		contentType: EnumContentType.JSON,
		originData: false,
		errorTips: true,
		successTips: false,
		fullPath: false,
		ignoreGlobalAim: false,
		aim: '',
		loading: false,
		cacheResult: false,
		refreshCache: false,
		routeChangeCancel: true,
		holdRepeat: true,
		holdRepeatParams: true,
		useToken: true,
		toStr: true,
		arrayToStr: false,
		toURLSearchParams: false,
		toFormData: false,
		deleteEmptyParam: true,
		query: {},
	};
	//  用于存储每个请求的标识 和 取消函数
	private mapPendingSignal: Map<string, AbortController>;
	// 路由切换时，需要取消的请求
	private mapPendingSignalRoute: Map<string, true>;
	// 记录需要缓存数据的接口 key为 pendingKey value 为数据的值。只有请求成功的接口才会被记录
	private mapCacheResponse: Map<string, any>;
	// 记录等待中的 CallBack key 是 pendingKey ，value 是 当前pendingKey下所有的 CallBack的集合。每调用一次请求 就会返回一个  new Promise 所以用数组
	private mapCallBackResolves: Record<string, any[]>; // 成功状态的 CallBack
	private mapCallBackRejects: Record<string, any[]>; // 失败状态的 CallBack
	private errorTipsTimer: boolean; // 错误弹框可能会同时弹出多次，加一个防抖判断
	private isDev: boolean; //  是否处在开发环境
	public tokenKey: string;
	private globalAim: '' | TEnumAim;
	private defaultAim: '' | TEnumAim;
	public originHref: string;
	private getToken?: () => Promise<string | null>;
	private onFulfilled?: (conf: AxiosRequestConfig, rel: AxiosResponse<Result>) => Promise<any>; // 用户自定义的，请求返回时的处理方法

	public messageSuccess: (test: string) => void;
	public messageError: (test: string) => Promise<any>;
	public showLoading: (test?: any) => void;
	public hideLoading: (test?: any) => void;
	public resultCode: { success: string } & ResultCodeCustom; // 用户自定义的，请求返回时的处理方法

	constructor(options: AxiosRequestConfig, other: IAxiosOptionsCreate<TEnumAim, ResultCodeCustom>) {
		this.optionsTrue = Object.assign(cloneDeep(this.defaultOptions), other || {});
		this.axiosInstance = axios.create(options);
		// 请求拦截器配置处理
		// this.axiosInstance.interceptors.request.use(requestFulfilled, requestRejected);
		// 响应结果拦截器处理
		this.axiosInstance.interceptors.response.use((rel) => rel, onRejected);
		this.mapPendingSignal = new Map<string, AbortController>();
		this.mapPendingSignalRoute = new Map<string, true>();
		this.mapCacheResponse = new Map<string, any>();
		this.mapCallBackResolves = {};
		this.mapCallBackRejects = {};
		this.errorTipsTimer = false;
		this.isDev = other?.isDev || false;
		this.tokenKey = other?.tokenKey || 'token';
		this.globalAim = other?.globalAim || '';
		this.defaultAim = other?.defaultAim || '';
		this.originHref = other?.originHref ?? location.origin;
		this.getToken = other?.getToken;
		this.onFulfilled = other?.onFulfilled;
		this.messageSuccess = other?.messageSuccess;
		this.messageError = other?.messageError;
		this.showLoading = other?.showLoading;
		this.hideLoading = other?.hideLoading;
		this.resultCode = (other?.resultCode as any) || { success: 0 };
	}
	setOriginHref(str: string) {
		this.originHref = str;
	}

	getRootUrl(url: string, options: Pick<IAxiosOptions<TEnumAim>, 'aim' | 'ignoreGlobalAim'>) {
		if (this.isDev && (options?.aim || this.globalAim || this.defaultAim)) {
			// 开发环境
			const aim_ = this.globalAim ? (options?.ignoreGlobalAim ? options?.aim : this.globalAim) : options?.aim;
			url = `${this.originHref}${aim_ || this.defaultAim}${url}`;
		} else {
			url = `${this.originHref}${url}`;
		}
		return url;
	}

	// 清除所有缓存数据
	clearCache() {
		this.mapCacheResponse.clear();
		this.mapCallBackResolves = {};
		this.mapCallBackRejects = {};
	}

	//  移除 Pending 不取消请求
	removePending(pendingKey: string) {
		if (this.mapPendingSignal.has(pendingKey)) {
			this.mapPendingSignal.delete(pendingKey);
			this.mapPendingSignalRoute.delete(pendingKey);
		}
	}
	// 取消所有请求接口
	cancelAllPending() {
		this.mapPendingSignal.forEach((controller) => {
			controller.abort();
		});
		this.mapPendingSignal.clear();
		this.mapPendingSignalRoute.clear();
	}
	// 路由改变时调用这个方法
	cancelAllPendingByRoute() {
		this.mapPendingSignal.forEach((controller, pendingKey) => {
			if (this.mapPendingSignalRoute.has(pendingKey)) {
				controller.abort();
				this.mapPendingSignal.delete(pendingKey);
				this.mapPendingSignalRoute.delete(pendingKey);
			}
		});
	}
	//  取消单个请求
	cancelPending(pendingKey: string) {
		if (this.mapPendingSignal.has(pendingKey)) {
			// 如果在 pending 中存在当前请求标识，需要取消当前请求，并且移除
			const controller = this.mapPendingSignal.get(pendingKey);
			controller && controller.abort();
			this.mapPendingSignal.delete(pendingKey);
			this.mapPendingSignalRoute.delete(pendingKey);
		}
	}
	private getOptions(options?: IAxiosOptions<TEnumAim>): IAxiosOptionsTrue<TEnumAim> {
		const { aim, ...other } = options || {};
		return Object.assign({}, this.optionsTrue, { aim, ...other });
	}
	request<T = any>(conf_: AxiosRequestConfig, options_?: IAxiosOptions<TEnumAim>): Promise<T> {
		const options = this.getOptions(options_);
		const {
			cacheResult,
			refreshCache,
			holdRepeat,
			holdRepeatParams,
			routeChangeCancel,
			useToken,
			ignoreGlobalAim,
			headers,
			fullPath,
			aim,
			loading,
			config,
			contentType,
			toStr,
			arrayToStr,
			toURLSearchParams,
			toFormData,
			deleteEmptyParam,
			query,
		} = options;
		const { headers: headers__, params: params__, ...otherConfig } = config;
		const headers_: Record<string, any> = {
			'Content-Type': contentType,
			// 'X-Frame-Options': 'AllowAll',
			...conf_.headers,
			...headers__,
			...headers,
		};

		const conf = Object.assign(conf_, otherConfig, { headers: headers_ });
		let pendingKey: string;
		if (cacheResult === true) {
			pendingKey = getPendingKey(conf, holdRepeatParams);
			if (refreshCache === false) {
				// 如果不需要刷新缓存数据
				// 如果数据缓存中存在值 直接返回
				if (this.mapCacheResponse.has(pendingKey)) {
					return Promise.resolve(cloneDeep(this.mapCacheResponse.get(pendingKey)));
				}
				// 如果 mapCallBackResolves 存在值 的话，说明接口正在发送中。先将 CallBack 存起来，等接口返回时统一处理
				if (this.mapCallBackResolves[pendingKey]) {
					return new Promise((resolve, reject) => {
						this.mapCallBackResolves[pendingKey].push(resolve);
						this.mapCallBackRejects[pendingKey].push(reject);
					});
				}
			}

			// 如果都不存在说明是第一次发送请求 给 mapCallBackResolves 赋空值 以便记录后续的请求
			this.mapCallBackResolves[pendingKey] = [];
			this.mapCallBackRejects[pendingKey] = [];
		}
		// 开启全局loading
		loading && this.showLoading();
		if (!fullPath) {
			// 如果不是 全路径，则拼接前缀
			conf.url = this.getRootUrl(conf.url!, {
				aim,
				ignoreGlobalAim,
			});
		}
		if (conf.method === 'get' || conf.method === 'GET') {
			const paramsAll = {
				...params__,
				...conf.params,
			};
			if (deleteEmptyParam) {
				// 去掉值为空的参数
				Object.keys(paramsAll).forEach((key) => {
					if (paramsAll[key] === '' || paramsAll[key] === undefined || paramsAll[key] === null) {
						delete paramsAll[key];
					}
				});
			}
			// get请求添加随机时间戳，防止缓存
			// paramsAll._t = new Date().getTime();

			conf.params = paramsAll;
			if (arrayToStr) {
				arrayToString(conf.params);
			}
		} else {
			const param: string[] = [];
			if (conf.data) {
				try {
					Object.keys(conf.data).forEach((key) => {
						if (conf.data[key] === undefined || (typeof conf.data[key] === 'number' && isNaN(conf.data[key]))) {
							conf.data[key] = '';
						}
					});
				} catch (error) {
					console.log('11111 Object.keys(conf.data)', error);
				}
			}
			for (let key in query) {
				param.push(key + '=' + encodeURIComponent(query[key]));
			}
			if (param.length) {
				conf.url += '?' + param.join('&');
			}

			if (arrayToStr) {
				arrayToString(conf.data);
			}
			if (toURLSearchParams) {
				const data_ = new URLSearchParams();
				for (const key in conf.data) {
					const item = conf.data[key];
					data_.append(key, item);
				}
				conf.data = data_;
			} else if (toFormData) {
				const data_ = new FormData();
				for (const key in conf.data) {
					const item = conf.data[key];
					data_.append(key, item);
				}
				conf.data = data_;
			} else if (toStr) {
				conf.data = JSON.stringify(conf.data);
			}
		}

		return Promise.resolve()
			.then(() => {
				if (useToken && this.getToken) {
					return this.getToken().then((token_) => {
						const token = conf.headers?.token || token_ || '';
						if (token) {
							conf.headers![this.tokenKey] = token;
						}
						return token_;
					});
				}
				return Promise.resolve('');
			})
			.then(() => {
				if (holdRepeat || routeChangeCancel) {
					pendingKey = pendingKey || getPendingKey(conf, holdRepeatParams);
					// 先取消上一个，
					this.cancelPending(pendingKey);
					const controller = new AbortController();
					conf.signal = controller.signal;
					this.mapPendingSignal.set(pendingKey, controller);
					if (routeChangeCancel) {
						this.mapPendingSignalRoute.set(pendingKey, true);
					}
				}
				return this.axiosInstance.request<any, AxiosResponse<Result>>(conf);
			})
			.then((res: AxiosResponse<Result>) => {
				if (this.onFulfilled) {
					return this.onFulfilled(conf, res);
				}
				return Promise.resolve(res);
			})
			.then((res: AxiosResponse<Result>) => {
				// console.log('请求回来了，不一定成功', res, conf);
				return this.transformRequestData(res, options);
			})
			.then((data) => {
				// 移除map对象中的值，不是取消请求
				this.removePending(pendingKey);
				if (cacheResult === true) {
					// console.log('请求回来了，成功  缓存数据', mapCallBackResolves);
					this.mapCacheResponse.set(pendingKey, data);
					if (this.mapCallBackResolves[pendingKey]) {
						this.mapCallBackResolves[pendingKey].forEach((func) => {
							func(data);
						});
						delete this.mapCallBackResolves[pendingKey];
						delete this.mapCallBackRejects[pendingKey];
					}
					// console.log('请求回来了，成功  缓存数据', mapCallBackResolves);
				}
				return data;
			})
			.catch((err: any) => {
				if (cacheResult === true && this.mapCallBackRejects[pendingKey]) {
					this.mapCallBackRejects[pendingKey].forEach((func) => {
						func(err);
					});
					delete this.mapCallBackResolves[pendingKey];
					delete this.mapCallBackRejects[pendingKey];
				}
				console.log('请求回来了，失败', err, conf);
				return Promise.reject(err as Promise<T>);
			})
			.finally(() => {
				loading && this.hideLoading();
			});
	}
	get<T = any>(url: string, params?: any, options?: IAxiosOptions<TEnumAim>): Promise<T> {
		const conf: AxiosRequestConfig = {
			url,
			method: 'get',
			params: params,
		};
		return this.request<T>(conf, options);
	}
	post<T = any>(url: string, data: any, options?: IAxiosOptions<TEnumAim>): Promise<T> {
		const conf: AxiosRequestConfig = {
			url,
			method: 'post',
			data,
		};
		return this.request<T>(conf, options);
	}
	put<T = any>(url: string, data: any, options?: IAxiosOptions<TEnumAim>): Promise<T> {
		const conf: AxiosRequestConfig = {
			url,
			method: 'put',
			data: data,
		};
		return this.request<T>(conf, options);
	}
	delete<T = any>(url: string, data: any, options?: IAxiosOptions<TEnumAim>): Promise<T> {
		const conf: AxiosRequestConfig = {
			url,
			method: 'delete',
			params: data,
		};
		return this.request<T>(conf, options);
	}
	/**
	 * 二次封装 axios 的get方法
	 * @param url
	 */
	getUrl<T = any>(url: string): Promise<T> {
		return this.axiosInstance
			.get(url)
			.then((res: AxiosResponse<any, any>) => {
				// .then((res: AxiosResponse<Result, any>) => {
				if (res?.data?.code !== this.resultCode.success) {
					return Promise.reject(res.data);
				}
				return res.data as any;
				// return transformRequestData(res, options_);
			})
			.catch((err: Error) => {
				return Promise.reject(err as unknown as Promise<T>);
			});
	}
	transformRequestData(res: AxiosResponse<Result>, options: IAxiosOptionsTrue<TEnumAim>) {
		if (res?.data?.code == resultCodePrivate.cancel) {
			// 如果请求被取消  直接返回失败，不弹窗
			return Promise.reject(res);
		}
		const { originData, errorTips, successTips } = options;
		const resData = res?.data;
		if (originData) {
			return resData; // 不进行任何处理，直接返回原数据
		}
		//   code，data，message为 后台统一的格式，
		const { code, data, message } = resData || {};
		if (resData && code === this.resultCode.success) {
			if (successTips) {
				// 需要提示时，弹框提示
				const text = isString(successTips) ? successTips : message || '操作成功！';
				this.messageSuccess(text);
			}
			return data;
		} else {
			let msg = message || '操作失败！';
			if (!errorTips) {
				// 不需要提示时 返回
				return Promise.reject({
					code,
					message: msg,
				});
			}
			if (code === resultCodePrivate.error) {
				// 网络错误，网络错误可能是多个接口同时触发，加一个防抖 ,只弹出一个
				if (!this.errorTipsTimer) {
					this.errorTipsTimer = true;
					this.messageError(msg).finally(() => {
						this.errorTipsTimer = false;
					});
				}
			} else if (errorTips) {
				msg = isString(errorTips) ? errorTips : msg;
				this.messageError(msg);
			}
			return Promise.reject({
				code,
				message: msg,
			});
		}
	}
}
function arrayToString(obj?: Record<string, any> | null) {
	if (isObject(obj)) {
		for (const key in obj) {
			if (isArray(obj[key])) {
				obj[key] = obj[key] + '';
			}
		}
	}
}
const mapCheckStatus: Record<number | string, string> = {
	400: '',
	401: '用户没有权限（令牌、用户名、密码错误）!',
	403: '用户得到授权，但是访问是被禁止的!',
	404: '网络请求错误,未找到该资源!',
	405: '网络请求错误,请求方法未允许!',
	408: '网络请求超时!',
	500: '服务器错误,请联系管理员!',
	501: '网络未实现!',
	502: '网络错误!',
	503: '服务不可用，服务器暂时过载或维护!',
	504: '网络超时!',
	505: 'http版本不支持该请求!',
};
const resultCodePrivate = {
	error: 'error', // 当网络有问题时，请求拦截器返回的状态， 前端自定义的状态码
	cancel: 'ERR_CANCELED', // 请求被取消时 前端返回的状态码  ERR_CANCELED 是axios返回的值
};
/**
 *  请求返回失败的拦截器 此时大概率是网络不可用  或者 500  或者是用户取消了请求  此时返回 Result 在 transformRequestData 统一处理
 */
function onRejected(error: any) {
	console.log('请求返回失败的拦截器 ', error);
	let msg: string = '发生错误！';
	let code_: any = resultCodePrivate.error; //

	// 自定义返回值  请求被取消 经过测试 请求被取消时 前端返回值 为 code : "ERR_CANCELED"   message: "canceled"
	if (error?.code === resultCodePrivate.cancel || error?.message === 'canceled') {
		code_ = resultCodePrivate.cancel;
		msg = '请求被取消';
	} else {
		const { response, code, message } = error || {};
		msg = response?.data?.error?.message || msg;
		const err: string = error.toString();
		if (code === 'ECONNABORTED' && message.indexOf('timeout') !== -1) {
			msg = '接口请求超时,请刷新页面重试!';
		} else if (err && err.includes('Network Error')) {
			msg = '网络异常,请检查您的网络连接是否正常!';
		} else {
			msg = mapCheckStatus[response?.status] || msg;
		}
	}
	return {
		data: {
			code: code_,
			message: msg,
			data: null,
		},
	};
}

// 根据 config 获取 mapPendingCanceler 的key。方便取消请求
function getPendingKey(config: AxiosRequestConfig, holdRepeatParams: boolean) {
	// 拦截重复请求时，是否包含参数，默认 true ;为 false 时， url相同时便会被拦截
	const arr: string[] = [config.method!, config.url!];
	if (holdRepeatParams) {
		arr.push(JSON.stringify(config.data || {}), JSON.stringify(config?.params || {}));
	}
	return arr.join('&');
}

export interface Result<T = any> {
	code: string | number;
	message: string;
	data?: T;
}
/**
 *    常用的contentType类型
 */
export enum EnumContentType {
	JSON = 'application/json;charset=UTF-8',
	TEXT = 'text/plain;charset=UTF-8',
	FORM_URLENCODED = 'application/x-www-form-urlencoded;charset=UTF-8',
	FORM_DATA = 'multipart/form-data;charset=UTF-8',
}

export interface IAxiosOptions<TEnumAim> {
	/**
	 * 该参数将扩展到 axios 的 config 中
	 */
	config?: AxiosRequestConfig;
	/**
	 * 如果 config 中存在 headers 则会 叠加
	 */
	headers?: AxiosRequestConfig['headers'];
	/**
	 * contentType
	 * @default EnumContentType.JSON
	 */
	contentType?: EnumContentType;
	/**
	 * 返回完整的Result数据，包含code data message等。默认 false 即对请求结果中的code进行状态判断，并返回data；如果不需要，可设置为true，
	 * @default false
	 */
	originData?: boolean;
	/**
	 * 弹窗提示失败信息， 默认 true, 为 false 时不提示 | 为true 时提示Result.message | string 时，提示string的内容
	 * @default true
	 */
	errorTips?: boolean | string;
	/**
	 * 弹窗提示成功信息， 默认 false, 为 false 时不提示 | 为true 时提示Result.message | string 时，提示string的内容
	 * @default false
	 */
	successTips?: boolean | string;
	/**
	 * 是否是全路径 默认 false, 为 true时，则不在路径中拼接任何前缀，
	 * @default false
	 */
	fullPath?: boolean;
	/**
	 * api访问环境地址，仅在开发环境下生效 优先级 globalAim > aim
	 * @default 'default'
	 */
	aim?: TEnumAim | '';

	/**
	 * 每个接口都可以有自己的 aim 如果 ignoreGlobalAim 为 true ，则 忽略 globalAim 。即该接口的优先级 aim > globalAim 仅在开发环境下生效
	 * @default false
	 */
	ignoreGlobalAim?: boolean;
	/**
	 * 开启全局 loading 默认false ;为true时，在接口访问期间，自动开启全局的loading，访问结束后关闭
	 * @default false
	 */
	loading?: boolean;
	/**
	 * 缓存数据接口数据，默认 false ; 重复请求url和参数相同api时，将不在发送真实的api请求，而是直接取缓存中的数据。请谨慎使用，
	 * @default false
	 */
	cacheResult?: boolean;
	/**
	 * 本次调用时刷新 缓存数据 仅在 cache 为true 的情况下生效
	 * @default false
	 */
	refreshCache?: boolean;
	/**
	 * 拦截重复请求 ，默认 true ; 默认状态下，前端会拦截短时间内url和参数相同的重复请求, 将上一个请求取消掉，如果为false 则前端不做任何拦截，
	 * @default true
	 */
	holdRepeat?: boolean;
	/**
	 * 拦截重复请求时，是否包含参数，默认 true，既url和参数都相同时，会拦截 ;为 false 时， url相同时便会被拦截
	 * @default true
	 */
	holdRepeatParams?: boolean;
	/**
	 * 路由改变时是否取消请求 ，在 pending 状态时，路由改变的话将取消请求，
	 * @default true
	 */
	routeChangeCancel?: boolean;
	/**
	 * 访问接口时是否携带token 默认 true
	 * @default true
	 */
	useToken?: boolean;

	/**
	 * 是否将参数转换成 string
	 * @default
	 */
	toStr?: boolean;
	/**
	 * 是否将Array类型的参数转转换成string
	 * @default false
	 */
	arrayToStr?: boolean;
	/**
	 * 是否将参数转换成 URLSearchParams 默认 false
	 * @default false
	 */
	toURLSearchParams?: boolean;
	/**
	 * 是否将参数转换成 FormData 默认 false
	 * @default false
	 */
	toFormData?: boolean;
	/**
	 * 是否将 get 请求中的空参数删掉，比如 ： name:'' 时，将不传 name
	 * @default false
	 */
	deleteEmptyParam?: boolean;
	/**
	 * 添加到路径中的参数，目前仅非get类型的请求使用
	 */
	query?: Record<string, any>;
}
// 创建实例时需要的参数
export interface IAxiosOptionsCreate<TEnumAim, ResultCodeCustom> extends IAxiosOptions<TEnumAim> {
	/**
	 * 是否处在开发环境
	 * @default false
	 */
	isDev?: boolean;
	/**
	 * 传入接口 Request Headers中的 token 的 key
	 * @default 'token'
	 */
	tokenKey?: string;
	/**
	 * api访问环境地址，  仅在开发环境下生效 优先级 globalAim > aim > defaultAim
	 */
	globalAim?: TEnumAim | '';
	/**
	 * api访问环境地址，  仅在开发环境下生效 优先级 globalAim > aim > defaultAim
	 */
	defaultAim?: TEnumAim | '';
	/**
	 *  接口请求的地址前拼接的地址 默认是 location.origin
	 * @default location.origin
	 */
	originHref?: string;
	/**
	 * 获取token的方法
	 */
	getToken?: () => Promise<string | null>;
	/**
	 * 用户自定义的，请求返回时的处理方法 请求返回后执行
	 */
	onFulfilled?: (conf: AxiosRequestConfig, rel: AxiosResponse<Result>) => Promise<any>;
	/**
	 * 接口成功时的弹窗方法
	 */
	messageSuccess: (test: string) => void;
	/**
	 * 接口失败时的弹窗方法
	 */
	messageError: (test: string) => Promise<any>;
	/**
	 * 开启全局loading的方法
	 */
	showLoading: (test: any) => void;
	/**
	 * 关闭全局loading的方法
	 */
	hideLoading: (test: any) => void;
	/**
	 * 返回值里 code 的枚举值
	 */
	resultCode?: ResultCodeCustom;
}
// 将传入的属性变为必选项,
export type IAxiosOptionsTrue<TEnumAim> = Required<IAxiosOptions<TEnumAim>>;
