<template>
	<right-box :title="title">
		<el-form
			ref="refForm"
			:model="formState"
			:disabled="handleType === 'view'"
			label-position="top"
			:rules="rules"
			label-width="100px"
		>
			<el-form-item label="日期" prop="typeId">
				<el-date-picker v-model="formState.date" type="date" />
			</el-form-item>
			<el-form-item label="名字" prop="name">
				<el-input v-model="formState.name" placeholder="请输入"> </el-input>
			</el-form-item>
			<el-form-item label="地址" prop="address">
				<el-input v-model="formState.address" placeholder="请输入"> </el-input>
			</el-form-item>
			<el-form-item label="标签" prop="address">
				<el-select v-model="formState.tag" placeholder="请选择">
					<el-option v-for="item in tagList" :label="item.label" :value="item.value" />
				</el-select>
			</el-form-item>
		</el-form>
		<template #foot>
			<el-button size="small" @click="onClick1">关闭</el-button>
			<el-button type="primary" v-if="handleType !== 'view'" size="small" @click="onClick2">提交</el-button>
		</template>
	</right-box>
</template>

<script lang="ts" setup>
import { ref, watch, inject } from 'vue';
import { cloneDeep } from 'lodash-es';
import { ResponseInfo } from './api';
import { rule_true_all } from '@/utils/validator';
import { setObjValue } from '@/utils';
import { nextTick } from 'vue';

interface Props {
	rowData?: any;
	title: string;
	handleType: 'add' | 'view' | 'edit' | string; //
}
const props = withDefaults(defineProps<Props>(), {});
const emit = defineEmits<{}>();
const toggleRight = inject<ToggleContainerRight>('toggleRight')!; // 调用父组件中的方法
const refForm = ref<ElFormInstance>();

const defaultForm: ResponseInfo = {
	date: '',
	name: '',
	address: '',
	tag: '',
};
const formState = ref<ResponseInfo>(cloneDeep(defaultForm));
// const rules = Object.keys(defaultForm).reduce((a, b) => {
// 	a[b] = rule_true_all;
// 	return a;
// }, {} as FormRules<ResponseInfo>);
const rules: FormRules<ResponseInfo> = {
	date: rule_true_all,
	name: rule_true_all,
};

watch(
	() => props.rowData,
	(value) => {
		console.log('111111111111111111111111', value);
		setObjValue(formState.value, value);
		setTimeout(() => {
			refForm.value?.clearValidate();
		}, 10);
	},
	{
		immediate: true,
	},
);

function onClick1() {
	toggleRight(false);
}
function onClick2() {
	refForm.value?.validate().then(() => {
		ElMessage.success('提交成功');
		toggleRight(false, {
			refresh: true, // 传入特殊字段,在外部刷新列表
		});
	});
}
const tagList: OptionItem[] = [
	{
		value: 'tag1',
		label: '标签1',
	},
	{
		value: 'tag1',
		label: '标签1',
	},
];
</script>
<style lang="scss" scoped></style>
