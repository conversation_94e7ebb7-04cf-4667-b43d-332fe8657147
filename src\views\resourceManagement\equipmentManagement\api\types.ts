export interface getListRequestType {
	key?: string;
	pageNo: number;
	pageSize: number;
	[property: string]: any;
}

/**
 * PageResultEquipmentBaseDTO
 */
export interface PageResultEquipmentBaseDTO {
	list: EquipmentBaseDTO[];
	total: number;
	[property: string]: any;
}

/**
 * EquipmentBaseDTO
 */
export interface EquipmentBaseDTO {
	bindStatus?: number;
	chassisNumber?: string;
	/**
	 * 物联网iot设备类型
	 * MAGNETIC_CAR_POSITION  亚米级磁吸车载定位接收机
	 * PERSONNEL_HANDHELD_POSITION 4G全网通人员手持定位终端
	 * VIDEO_DEVICE  摄像头设备
	 * 设备状态：0关闭 1开启
	 */
	ciotModelType?: string;
	cityCode: string; //地市
	deviceModelName: string; //设备型号
	deviceNumber: string; //设备编码
	districtCode: string; //区县
	engineNumber?: string; //发动机编号
	equipmentBrand: string; //林机品牌
	equipmentCode: string; //林机类型
	factoryNumber: string; //出厂编号
	jobType: string; //服务类型
	latitude?: string; //纬度坐标
	longitude?: string; //经度坐标
	machineWidth: string; //林机幅宽(米)
	ownerName: string; //服务队或个人名称
	ownerType: number; //所属人类型：0组织 1个人
	ownerUuid: string; //服务队或个人uuid
	power: string; //功率(千瓦)
	provinceCode: string; //省
	remark?: string; //备注信息
	status?: number; //设备状态：0关闭 1开启
	terminalNumber?: string; //终端编号
	uuid: string; //唯一编码
	[property: string]: any;
}

/**
 * SaveEquipmentDTO
 */
export interface SaveEquipment {
	chassisNumber?: string; //底盘号
	cityCode?: string; //地市
	deviceModelName?: string; //设备型号
	deviceNumber?: string; //设备编码
	districtCode?: string; //区县
	engineNumber?: string; //发动机编号
	equipmentBrand?: string; //林机品牌
	equipmentCode?: string; //林机类型
	factoryNumber?: string; //出厂编号
	jobType?: string; //作业类型
	latitude?: string; //维度
	longitude?: string; //经度
	machineWidth?: string; //林机幅宽(m)
	ownerName?: string; //姓名
	ownerPhone?: string; //手机号
	ownerType?: number; //所属人类型：0组织 1个人
	ownerUuid?: string; //服务队或个人uuid
	power?: string; //功率(千瓦)
	provinceCode?: string; //省
	remark?: string; //备注
	terminalNumber?: string; //终端编号
	[property: string]: any;
}

export interface RequestStatus {
	status?: number;
	uuids?: string[];
	[property: string]: any;
}

export interface RequestTeamList {
	cityCode?: string;
	districtCode?: string;
	provinceCode?: string;
	tenantId?: string;
	[property: string]: any;
}

export interface TeamList {
	teamName: ''; //区域总面积
	teamUuid: ''; //区域完成总面积
	[property: string]: any;
}

export interface RequestBindDeviceList {
	number?: string; // 搜索关键字
}

export interface BindDeviceList {
	deviceNumber: ''; // 设备编号
	productName: ''; // 设备名称
}
