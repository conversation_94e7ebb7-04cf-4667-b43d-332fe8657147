<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.ico" />
		<title>首页</title>
		<script>
			function isIE1() {
				return navigator.userAgent.toLowerCase().indexOf('trident') > -1;
			}
			function isIE2() {
				return !!window.ActiveXObject || 'ActiveXObject' in window;
			}
			if (isIE1() || isIE2()) {
				alert('本系统暂不支持IE浏览器，建议您使用最新版的谷歌浏览器。');
			}
		</script>
		<style>
			@keyframes antSpinMove {
				to {
					opacity: 1;
				}
			}

			@keyframes antRotate {
				to {
					transform: rotate(405deg);
				}
			}

			/* 全局loading的样式 */
			.g-loading-box {
				position: absolute;
				box-sizing: border-box;
				transition: all 0.3s;
				top: 0;
				bottom: 0;
				right: 0;
				left: 0;
				z-index: 99999;
				background-color: rgba(255, 255, 255, 0.6);
			}

			.g-loading-dot-spin {
				position: absolute;
				top: 50%;
				left: 50%;
				margin: -10px;
				font-size: 20px;
				width: 1em;
				height: 1em;
				transform: rotate(45deg);
				animation: antRotate 1.2s infinite linear;
			}

			.g-loading-dot-item {
				position: absolute;
				display: block;
				width: 9px;
				height: 9px;
				background-color: #b6e2ff;
				border-radius: 100%;
				transform: scale(0.75);
				transform-origin: 50% 50%;
				opacity: 0.3;
				animation: antSpinMove 1s infinite linear alternate;
			}

			.g-loading-dot-item:nth-child(1) {
				top: 0;
				left: 0;
			}

			.g-loading-dot-item:nth-child(2) {
				top: 0;
				right: 0;
				animation-delay: 0.4s;
			}

			.g-loading-dot-item:nth-child(3) {
				right: 0;
				bottom: 0;
				animation-delay: 0.8s;
			}

			.g-loading-dot-item:nth-child(4) {
				bottom: 0;
				left: 0;
				animation-delay: 1.2s;
			}
		</style>
		<script>
			(function () {
				//判断浏览器版本

				// 监听G_loading,快速改变全局loading的状态
				// 开启全局loading : G_loading.value = true
				// 放到html中是为了第一时间显示出loading，提升用户体验
				let timer = null;
				window.G_loading = new Proxy(
					{ value: true },
					{
						set(target, key, val) {
							if (target[key] !== val) {
								const rootDom = document.getElementById('global_loading_box_id');
								if (val) {
									// 先显示遮罩，防止用户重复点击，延迟显示背景，避免闪屏问题
									rootDom.style.opacity = '0';
									rootDom.style.display = 'block';
									setTimeout(() => {
										rootDom.style.opacity = '1';
									}, 10);
								} else {
									rootDom.style.display = 'none';
								}
							}
							target[key] = val;
							return true;
						},
					},
				);
			})();
		</script>
		<script src="/iconfont.js"></script>
	</head>

	<body>
		<!--[if IE]> 本系统暂不支持IE浏览器，建议您使用最新版谷歌浏览器。 <![endif]-->
		<div id="global_loading_box_id" class="g-loading-box">
			<!-- 全局加载loading -->
			<div class="g-loading-dot-spin">
				<i class="g-loading-dot-item"></i>
				<i class="g-loading-dot-item"></i>
				<i class="g-loading-dot-item"></i>
				<i class="g-loading-dot-item"></i>
			</div>
		</div>
		<div id="app" style="position: relative"></div>
		<script src="/configs/cqtduapi.js" type="text/javascript"></script>
		<script type="module" src="/src/main.ts"></script>
	</body>
</html>
