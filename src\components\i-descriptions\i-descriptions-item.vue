<template>
	<div class="i-descriptions-item" :class="className">
		<div class="i-descriptions-label" v-if="label">
			<slot :name="label">{{ label }}</slot>
		</div>
		<div class="i-descriptions-content">
			<slot> - </slot>
		</div>
	</div>
</template>

<script lang="ts" setup>
interface Props {
	label?: string; // 一行 Descriptions Item 的数量
	className?: string; // 自定义类 目前预设了一个 g-fill  占满一行
}
const props = withDefaults(defineProps<Props>(), {});
</script>

<style lang="scss" scoped>
.i-descriptions-item {
	font-size: 14px;
	display: flex;
	align-items: center;
	flex-shrink: 0;
	flex-grow: 0;
	width: var(--item-width);
	box-sizing: border-box;
	&:last-of-type {
		flex-grow: 1;
		margin-bottom: 0;
	}
	.i-descriptions-label {
		min-width: var(--label-width);
		flex-shrink: 0;
		flex-grow: 0;
		display: flex;
		align-items: center;
		padding: 7px 8px 7px 16px;
		box-sizing: border-box;
		// color: var(--el-color-info);
		color: #444857;
		// margin-right: 10px;
	}
	.i-descriptions-content {
		flex-grow: 1;
		padding: 2px 8px;
		width: calc(var(--item-width) - var(--label-width));
		word-wrap: break-word;
		word-break: break-all;
		white-space: normal;
		display: flex;
		align-items: center;
		box-sizing: border-box;
		flex-wrap: wrap;
		&:empty::after {
			content: '-';
		}
	}
	&.g-fill {
		flex-direction: column;
		align-items: flex-start;
		.i-descriptions-content {
			width: 100%;
		}
	}
}
</style>
