<template>
	<el-dialog
		v-model="dialogVisible"
		width="1000px"
		:title="title"
		:close-on-click-modal="false"
		append-to-body
		align-center
		destroy-on-close
		@closed="closeDialog"
		@open="handleOpen"
	>
		<el-form
			ref="formRef"
			style="margin: 0 12px"
			:model="formData"
			:disabled="type == 'view'"
			label-width="120px"
			:rules="formRules"
		>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="设备归属" prop="ownerType">
						<el-radio-group @change="formData.ownerUuid = ''" v-model="formData.ownerType">
							<el-radio :value="0">组织</el-radio>
							<el-radio :value="1">个人</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="出厂编号" prop="name">
						<el-input placeholder="请输入出厂编号" v-model="formData.name"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="行政区划" prop="address">
						<el-cascader
							:props="{
								value: 'areaId',
								label: 'areaName',
								children: 'children',
							}"
							placeholder="请选择行政区划"
							style="width: 100%"
							v-model="formData.address"
							:options="areas"
							@change="handleChange"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="设备型号" prop="deviceModelName">
						<el-input placeholder="请输入设备型号" v-model="formData.deviceModelName"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item v-if="formData.ownerType == 0" label="归属单位" prop="ownerUuid">
						<el-select v-model="formData.ownerUuid" placeholder="请选择所属服务队">
							<el-option v-for="item in teamListOptions" :key="item.teamUuid" :label="item.teamName" :value="item.teamUuid" />
						</el-select>
					</el-form-item>
					<el-form-item v-if="formData.ownerType == 1" label="所属人" prop="ownerName">
						<el-input placeholder="请输入所属人姓名" v-model="formData.ownerName"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="发动机编号" prop="engineNumber">
						<el-input placeholder="请输入发动机编号" v-model="formData.engineNumber"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24" v-if="formData.ownerType == 1">
				<el-col :span="12">
					<el-form-item label="联系方式" prop="ownerPhone">
						<el-input placeholder="请输入联系方式" v-model="formData.ownerPhone"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<!-- <el-col :span="12">
					<el-form-item label="设备编号" prop="deviceNumber">
						<el-input placeholder="请输入设备编号" :disabled="type == 'edit'" v-model="formData.deviceNumber"></el-input>
					</el-form-item>
				</el-col> -->
				<el-col :span="12">
					<el-form-item label="底盘号/机架号" prop="chassisNumber">
						<el-input placeholder="请输入底盘号/机架号" v-model="formData.chassisNumber"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="林机类型" prop="equipmentCode">
						<el-input placeholder="请输入林机类型" v-model="formData.equipmentCode"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="林机幅宽（m）" prop="machineWidth">
						<el-input placeholder="请输入林机幅宽" v-model="formData.machineWidth"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="服务类型" prop="jobType">
						<el-select v-model="formData.jobType" placeholder="请选择服务类型">
							<el-option v-for="item in center_service_type" :key="item.code" :label="item.name" :value="item.code" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="机具点位" prop="longitude">
						<el-input :disabled="type == 'edit'" style="width: 35%" v-model="formData.longitude" placeholder="东经度">
						</el-input>
					</el-form-item>
					<el-form-item label="" prop="latitude" class="latFormItem" style="position: absolute; top: 0; right: 0">
						<el-input :disabled="type == 'edit'" style="width: 58%" v-model="formData.latitude" placeholder="北纬度"> </el-input>
					</el-form-item>
					<el-icon
						:disabled="type == 'edit'"
						@click.native="chooseAddress"
						:size="24"
						:color="'#909399'"
						style="position: absolute; right: 40px; top: 4px; cursor: pointer"
					>
						<Location />
					</el-icon>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="林机品牌" prop="equipmentBrand">
						<el-input placeholder="请输入林机品牌" v-model="formData.equipmentBrand"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="终端编号" prop="terminalNumber">
						<el-input placeholder="请输入终端编号" :disabled="type == 'edit'" v-model="formData.terminalNumber"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="功率（千瓦）" prop="power">
						<el-input placeholder="请输入机具功率" v-model="formData.power"></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="备注" prop="remark">
						<el-input placeholder="请输入备注信息" v-model="formData.remark"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="出厂编号" prop="factoryNumber">
						<el-input placeholder="请输入出厂编号" v-model="formData.factoryNumber"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<el-row justify="center">
			<el-button @click="closeDialog" style="margin-right: 12px">取消</el-button>
			<el-button type="primary" @click="submit" v-if="type !== 'view'" style="margin-right: 12px">确认</el-button>
		</el-row>
		<!-- 地图弹窗 -->
		<tiandiShow
			ref="maptiandi"
			v-if="mapShow"
			:mapInitAddress="mapInitAddress"
			@emitAdressMethod="emitAdressMethod"
			@close="mapShow = false"
			:action="type == 'view' ? 'detail' : 'edit'"
		></tiandiShow>
	</el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ElForm, FormRules } from 'element-plus';
import { SaveEquipment, EquipmentBaseDTO, TeamList } from '../api/types';
import { api_editCenter, api_getTeamList, api_saveCenter } from '../api';
import { watch } from 'vue';
import { setObjValue } from '@/utils';
import { api_getArea, api_getDict } from '@/api/dict';
import { Location } from '@element-plus/icons-vue';
import { storeUserInfo } from '@/store';
interface Props {
	type: string; //新增还是查看还是编辑
	rowData?: EquipmentBaseDTO | null;
}
const props = withDefaults(defineProps<Props>(), {
	type: 'add',
});
const dialogVisible = defineModel<boolean>('visible');

const title = computed(() => {
	switch (props.type) {
		case 'add':
			return '新增';
		case 'view':
			return '查看';
		case 'edit':
			return '编辑';
		default:
			break;
	}
});
const emits = defineEmits<{
	(ev: 'saveForm'): void;
}>();

const teamListOptions = ref<TeamList[]>([]);
const center_service_type = ref<DicDataItemCode[]>([]);
onMounted(() => {});
const defaultData: SaveEquipment = {
	chassisNumber: '', //底盘号
	cityCode: '', //地市
	deviceModelName: '', //设备型号
	// deviceNumber: '', //设备编码
	districtCode: '', //区县
	engineNumber: '', //发动机编号
	equipmentBrand: '', //林机品牌
	equipmentCode: '', //林机类型
	factoryNumber: '', //出厂编号
	jobType: '', //作业类型
	latitude: '', //维度
	longitude: '', //经度
	machineWidth: '', //林机幅宽(m)
	ownerName: '', //姓名
	ownerPhone: '', //手机号
	ownerType: 0, //所属人类型：0组织 1个人
	ownerUuid: '', //服务队或个人uuid
	power: '', //功率(千瓦)
	provinceCode: '', //省
	remark: '', //备注
	terminalNumber: '', //终端编号

	address: [], // 省市县编码
};

const formData = ref<SaveEquipment>(Object.assign({}, defaultData, { subsidyRule: [] }));
const rules: FormRules<SaveEquipment> = Object.keys(formData.value).reduce((acc, key) => {
	acc[key] = [
		{
			required: true,
			message: '不能为空',
			trigger: 'blur',
		},
	];
	return acc;
}, {} as FormRules);

const formRules = ref<FormRules<SaveEquipment>>(rules);
delete formRules.value.deviceModelName;
delete formRules.value.remark;
delete formRules.value.engineNumber;
delete formRules.value.chassisNumber;
delete formRules.value.machineWidth;
delete formRules.value.latitude;
delete formRules.value.longitude;
delete formRules.value.terminalNumber;
delete formRules.value.power;
delete formRules.value.equipmentBrand;
delete formRules.value.factoryNumber;
const formRef = ref<InstanceType<typeof ElForm>>();
function fetchTeamList() {
	api_getTeamList({
		provinceCode: formData.value.address ? formData.value.address[0] : '',
		cityCode: formData.value.address ? (formData.value.address[1] ? formData.value.address[1] : '') : '',
		districtCode: formData.value.address ? (formData.value.address[2] ? formData.value.address[2] : '') : '',
		tenantId: storeUserInfo.enterpriseId,
	}).then((res) => {
		teamListOptions.value = res;
	});
}
function handleOpen() {
	api_getDict('center_service_type').then((res) => {
		center_service_type.value = res;
	});
	api_getArea().then((res) => {
		areas.value = res;
	});
	setObjValue(formData.value, {}, defaultData);
	if (!dialogVisible.value || props.type == 'add') return;
	setObjValue(formData.value, props.rowData!, defaultData);
	formData.value.address = [formData.value.provinceCode, formData.value.cityCode];
	if (formData.value.districtCode) formData.value.address.push(formData.value.districtCode);
	fetchTeamList();
}
function submit() {
	formRef.value?.validate((valid) => {
		if (!valid) return;
		let param = {
			chassisNumber: formData.value.chassisNumber, //底盘号
			cityCode: formData.value.cityCode, //地市
			deviceModelName: formData.value.deviceModelName, //设备型号
			// deviceNumber: formData.value.deviceNumber, //设备编码
			districtCode: formData.value.districtCode, //区县
			engineNumber: formData.value.engineNumber, //发动机编号
			equipmentBrand: formData.value.equipmentBrand, //林机品牌
			equipmentCode: formData.value.equipmentCode, //林机类型
			factoryNumber: formData.value.factoryNumber, //出厂编号
			// latitude: formData.value.latitude, //维度
			// longitude: formData.value.longitude, //经度
			machineWidth: formData.value.machineWidth, //林机幅宽(m)
			ownerType: formData.value.ownerType, //所属人类型：0组织 1个人
			power: formData.value.power, //功率(千瓦)
			provinceCode: formData.value.provinceCode, //省
			remark: formData.value.remark, //备注
			// terminalNumber: formData.value.terminalNumber, //终端编号
			jobType: formData.value.jobType, //服务范围 0,1,2
		};
		if (formData.value.ownerType == 0) {
			param['ownerUuid'] = formData.value.ownerUuid;
		} else {
			param['ownerName'] = formData.value.ownerName;
			param['ownerPhone'] = formData.value.ownerPhone;
		}
		let api_ = props.type == 'add' ? api_saveCenter : api_editCenter;
		if (props.type == 'edit') param['uuid'] = props.rowData!.uuid;
		if (props.type == 'add') {
			param['terminalNumber'] = formData.value.terminalNumber;
			// param['deviceNumber'] = formData.value.deviceNumber;
			param['latitude'] = formData.value.latitude;
			param['longitude'] = formData.value.longitude;
		}
		api_(param).then((res) => {
			emits('saveForm');
			dialogVisible.value = false;
		});
	});
}

function closeDialog() {
	setObjValue(formData.value, {}, defaultData);
	dialogVisible.value = false;
	formRef.value!.clearValidate();
	teamListOptions.value = [];
}

defineExpose({
	open,
	close,
});

const handleChange = (value) => {
	console.log(value);
	formData.value.provinceCode = value[0];
	formData.value.cityCode = value[1];
	if (value[2]) formData.value.districtCode = value[2];
	fetchTeamList();
};

const areas = ref<AreaTreeBase[]>([]);

function chooseAddress() {
	if (props.type == 'edit') return;
	mapShow.value = true;
}
const mapShow = ref<boolean>(false);
const mapInitAddress = ref({
	name: '',
	lng: '',
	lat: '',
});
function emitAdressMethod(e: any) {
	formData.value.latitude = e.lat;
	formData.value.longitude = e.lng;
	formRef.value!.clearValidate('latitude');
	formRef.value!.clearValidate('longitude');

	mapShow.value = false;
}
</script>
<style lang="scss" scoped>
.latFormItem {
	:deep(.el-form-item__content) {
		margin-left: 0 !important;
	}
}
</style>
