import { RouteLocationNormalized, RouteRecordRaw, Router, createRouter, createWebHistory } from 'vue-router';
import http from '@/utils/axios';

import { BASE_URL, envConfig, isDev, rootOrigin } from '@/config';
import { commonRoutes, mainRoutes } from './routes';
import { cloneDeep, isArray } from 'lodash-es';
import { setStoreMenus, setStoreSelectFirstMenu, storeMenus } from '@/store';
import { getPageTitle } from '@/utils';
import { getMenus, goToLogout } from '@/api/user';

mainRoutes.forEach((item) => {
	item.meta = item.meta || {};
	item.meta.icon = item.meta.icon || 'icon-all';
	item.meta.activeIcon = item.meta.activeIcon || 'icon-all';
});

const allRoutes = cloneDeep(commonRoutes);
console.log('allRoutes', allRoutes);

// 路由实例
export let routerInstance: Router;

export function initRouter(): Router {
	// objMain.children = cloneDeep(mainRoutes);
	// recursionTree(allRoutes); // 初始化时，暂时不用重定向
	console.log('allRoutes', allRoutes);
	routerInstance = createRouter({
		history: createWebHistory(BASE_URL),
		routes: allRoutes,
	});
	createRouterGuards(routerInstance);
	return routerInstance;
}

// 重置所有路由的重定向，重定向到第一个子路由中，
function recursionTree(list: RouteRecordRaw[]) {
	list.forEach((item) => {
		const { redirect, children } = item;
		if (children?.length) {
			if (!redirect) {
				item.redirect = {
					name: children[0].name,
				};
			}
			recursionTree(children);
		}
	});
}

/**
 * 创建路由守卫
 * @param router
 */
function createRouterGuards(router: Router) {
	router.beforeEach(async (to, from, next) => {
		console.log('即将跳转的路由：', to);
		const { title, pass } = to.meta || {};
		document.title = getPageTitle(title as string);
		http.cancelAllPendingByRoute();
		setStoreSelectFirstMenu(to.matched[0]);

		if (pass === true) {
			// 带有pass 的路由，不需要校验
			next();
		} else if (!storeMenus.value.length) {
			try {
				await getMenus().then((routers) => {
					setMainRoute(routers);
				});
				if (!storeMenus.value.length) {
					// 如果获取完，还是没有权限，404
					alert('该用户没有任何操作权限，请联系管理员配置权限后重新登录。');
					goToLogout(); // 调用登出接口,防止死循环
					next({ name: '404' });
				} else if (to.name === 'main' || to.path === '/main') {
					// 如果是 main 的话，需要重新重定向一次
					next({ name: 'main' });
				} else if (hasRoute(to, router)) {
					console.log('1111111111111 router.getRoutes()', router.getRoutes());
					// 一定要将to传进next 否则页面刷新时，当前路由没有name 无法识别
					next(to);
				} else {
					if (to.matched.find((item) => item.name === to.name)) {
						// 如果父级菜单中存在 main 跳转到main
						// 主要应对 刷新后菜单权限变化的情况；
						next({ name: 'main' });
						// window.location.href = rootOrigin + '/main';
					} else {
						next({ name: '404' });
					}
				}
			} catch (error) {
				console.log('11111111111111111111111 error', error);
				next({ name: '404' });
			}
		} else {
			next();
		}
	});
}

function hasRoute(to: RouteLocationNormalized, router: Router) {
	// 如果存在name 说明是页面内跳转过来的，不存在说明是刷新页面进来的
	if (to.name) {
		return router.hasRoute(to.name);
	} else {
		return !!router.getRoutes().find((item) => item.path === to.path);
	}
}

// 设置  MainRoute的 children
function setMainRoute(permission: ResponseMenusRouters[]) {
	const newRoutes = getChildren(permission);
	console.log('111111111111111111 newRoutes', newRoutes, newRoutes[0]);
	recursionTree(newRoutes);
	setStoreMenus(newRoutes);
	setStoreSelectFirstMenu(newRoutes[0]);
	const objMain = allRoutes.find((item) => item.name === 'main')!;
	objMain.redirect = {
		name: newRoutes[0].name,
	};
	routerInstance.addRoute(objMain);
	newRoutes.forEach((item) => {
		routerInstance.addRoute(item);
	});
}
// 根据 后台返回的数据，获取main的子路由
function getChildren(routers: ResponseMenusRouters[]): RouteRecordRaw[] {
	const mainChildren: RouteRecordRaw[] = []; // main 的子路由
	// const reportChildren: RouteRecordRaw[] = []; // 上报的子路由
	// let reportRoute: RouteRecordRaw | null = null;
	function self(list: RouteRecordRaw[], newList: RouteRecordRaw[]) {
		list.forEach((item) => {
			const { children, meta = {}, ...other } = item;
			let title = ''; // 菜单的名字，如果存在 说明需要显示该路由
			if (meta.authCode === '' || (meta.isDev && isDev)) {
				// 如果 meta.authCode 存在且为 '' 说明 不需要权限就可以查看的菜单  或者正在开发下的路由在开发模式下也可以直接显示
				title = meta.title as string;
			} else {
				const obj = routers.find((item_) => {
					//  如果不存在 authCode 使用name  authCode 可能是数组
					const key_ = (meta.authCode as string[] | string) || (item.name as string);
					return item_.authCode === key_ || (isArray(key_) && key_.includes(item_.authCode));
				});
				if (obj) {
					title = meta.titleLock ? (meta.title as string) : obj.name;
				}
			}

			if (title) {
				const newChildren: RouteRecordRaw[] = [];
				const meta_ = Object.assign(meta, { title: title });
				const newObj: RouteRecordRaw = {
					...other,
					meta: meta_,
					children: newChildren,
				};
				if (children && children.length) {
					// 将当前路由重定向到第一个子路由中
					self(children, newChildren);
					if (newChildren.length)
						newObj.redirect = {
							name: newChildren[0].name,
						};
				}
				newList.push(newObj);
			}
		});
	}
	self(mainRoutes, mainChildren);
	return mainChildren;
}
