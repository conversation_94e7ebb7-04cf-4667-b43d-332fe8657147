import http, { resolveFunc, rejectFunc, EnumAim } from '@/utils/axios';
import * as Types from './types';
import { isDev } from '@/config';
export * from './types';
const aim = EnumAim.test;

export enum Api {
	getList = '/web-api/test001', //  测试地址11
}

export function api_getList(param: any): Promise<Types.ResponseInfo[]> {
	const list = new Array(param.pageSize).fill('').map((item, index) => {
		return {
			date: '2016-05-03',
			name: '<PERSON>' + ((param.pageNumber - 1) * param.pageSize + index + 1),
			address: 'No. 189, Grove St, Los Angeles',
			tag: 'Home',
			state: index % 2 === 0 ? 10 : 20,
		};
	});
	if (isDev) return resolveFunc(list, 300);
	const url = Api.getList;
	return http.get(url, param, {
		aim,
	});
}
