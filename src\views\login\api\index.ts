import { isDev } from '@/config';
import http, { resolveFunc, rejectFunc, EnumAim } from '@/utils/axios';
import * as Types from './types';
export * from './types';
const aim = EnumAim.test;

export enum Api {
	login = '/userlogin', //  登录
}

export function api_login(param: Types.RequestLogin): Promise<Types.ResponseLogin> {
	const url = Api.login;
	return http.post(url, param, {
		aim,
		useToken: false,
		loading: true,
	});
}
