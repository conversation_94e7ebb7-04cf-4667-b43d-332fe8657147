import http, { resolveFunc, rejectFunc, EnumAim, XMLHttpDownload2, EnumContentType } from '@/utils/axios';
import * as Types from './types';
import { isDev } from '@/config';
const aim = EnumAim.test;

export enum Api {
	getList = '/web-api/service-user/v1/team/page', //  分页查询表格数据
	saveCenter = '/web-api/service-user/v1/team/save', //  保存服务队人员
	editCenter = '/web-api/service-user/v1/team/modify', //  修改服务队人员
	deleteCenter = '/web-api/service-user/v1/team/remove/', //  删除服务队人员
	importCenter = '/web-api/service-user/v1/team/import', //  导入服务队人员
	getCenterList = '/web-api/service-center/v1/list/for-relevancy', //  创建服务队-根据租户类型（角色）查询服务中心
	getTeamList = '/web-api/service-center/v1/list/for-relevancy-team', //  创建服务队-根据租户类型（角色）查询服务中心
}
export function api_getList(param: Types.getListRequestType): Promise<Types.PageResultCenterUserBaseVO> {
	const url = Api.getList;
	return http.get(url, param, { aim, loading: true }).then((res) => res[0] || {});
}

export function api_saveCenter(param: Types.RequestSaveCenter): Promise<any> {
	const url = Api.saveCenter;
	return http.post(url, param, { aim, successTips: true, loading: true });
}

export function api_editCenter(param: Types.RequestSaveCenter): Promise<any> {
	const url = Api.editCenter;
	return http.post(url, param, { aim, successTips: true, loading: true });
}

export function api_delete(serviceUuid: string) {
	const url = Api.deleteCenter + serviceUuid;
	return http.delete(url, null, { aim, successTips: false, loading: true });
}

export function api_import(file: any) {
	const url = Api.importCenter;
	return http.post(
		url,
		{ file: file },
		{ aim, contentType: EnumContentType.FORM_DATA, successTips: true, loading: true, toFormData: true },
	);
}
export function api_getCenterList(param: Types.RequestCenterList): Promise<Types.CenterList[]> {
	const url = Api.getCenterList;
	return http.get(url, param, { aim, loading: true }).then((res) => res || []);
}
export function api_getTeamList(param: Types.RequestTeamList): Promise<Types.TeamList[]> {
	const url = Api.getTeamList;
	return http.get(url, param, { aim, loading: true }).then((res) => res || []);
}
