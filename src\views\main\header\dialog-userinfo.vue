<template>
	<el-dialog v-model="modelValue_" title="个人信息" append-to-body align-center destroy-on-close width="600px">
		<el-form ref="refForm" class="t-form-box g-form-box" label-position="top">
			<el-form-item label="账号">
				<el-input :model-value="storeUserInfo.username" readonly />
			</el-form-item>
			<el-form-item label="姓名">
				<el-input :model-value="storeUserInfo.name" readonly />
			</el-form-item>
			<el-form-item label="电话">
				<el-input :model-value="storeUserInfo.telephone" readonly />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="modelValue_ = false">关闭</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { storeUserInfo } from '@/store';

interface Props {
	modelValue: boolean;
}
const props = withDefaults(defineProps<Props>(), {});
const emit = defineEmits<{
	(ev: 'update:modelValue', value: boolean): void;
}>();

const modelValue_ = computed({
	get() {
		return props.modelValue;
	},
	set(value) {
		emit('update:modelValue', value);
	},
});
</script>
<style lang="scss" scoped>
.t-form-box {
	margin: 20px;
}
</style>
