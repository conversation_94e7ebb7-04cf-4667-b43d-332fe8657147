<template>
	<div v-loading="loading_" class="i-common-table-box" :key="soleKey" ref="refDomBox">
		<div class="t-header" ref="refHeader" v-if="hideHeader === false || $slots.header">
			<div class="t-header-box">
				<slot name="header"> </slot>
			</div>
			<!-- <el-icon class="t-refresh-icon" title="刷新" @click="onRefresh"><RefreshRight /> </el-icon>
			<ISetting :columnOptions="columnOptions" :soleKey="soleKey" :size="($attrs.size as any)" @change="onChangeSetting"></ISetting> -->
		</div>
		<el-table
			v-bind="$attrs"
			:height="height_"
			@sort-change="onSortChange"
			@filter-change="onFilterChange"
			ref="refTable"
			:border="border"
			:showOverflowTooltip="showOverflowTooltip"
			:size="tableSize"
		>
			<el-table-column
				v-if="(showCheckBox || selectable) && !hideColumnKeys.includes('_select_')"
				:selectable="selectable"
				type="selection"
				fixed="left"
				align="center"
				width="50"
			/>
			<el-table-column
				v-if="showIndex && !hideColumnKeys.includes('_index_')"
				type="index"
				:index="renderIndex"
				align="center"
				class-name="t-cell-index"
				:width="50"
				label="序号"
			/>
			<template v-if="defaultSlotIndex && $slots.default">
				<template v-for="item in $slots.default()">
					<component v-if="!item.props?.prop || !hideColumnKeys.includes(item.props.prop)" :is="item"></component>
					<!-- :show-overflow-tooltip="
							item.props?.showOverflowTooltip ?? item.props?.['show-overflow-tooltip'] ?? showOverflowTooltip
						" -->
				</template>
			</template>
			<template v-if="columns && columns.length">
				<template
					v-for="{
						slotName,
						searchable,
						prop,
						states,
						dateFormat,
						key_,
						slotHeaderName,
						hide,
						formatter,
						filters,
						...column_
					} in columns"
				>
					<el-table-column
						:key="key_"
						v-if="!checkHide(hide) && (!prop || !hideColumnKeys.includes((prop as string)))"
						:prop="(prop as string)"
						:formatter="(formatter as any)"
						:filters="(filters as any)"
						v-bind="column_"
					>
						<!-- :show-overflow-tooltip="column_.showOverflowTooltip ?? column_['show-overflow-tooltip'] ?? showOverflowTooltip" -->
						<template v-if="searchable && prop" #header>
							<div class="t-search-header">
								{{ column_.label }}
								<el-popover placement="bottom-end" :width="200" trigger="click">
									<search-input
										v-model:value="mapColumnSearch[prop as string]"
										@search="onSearchInput({ prop, searchable })"
									/>
									<template #reference>
										<el-icon class="t-search-icon" @click.stop :class="{'t-search':!!mapColumnSearch[prop as string]}">
											<Search />
										</el-icon>
									</template>
								</el-popover>
							</div>
						</template>
						<template v-if="states?.length" #default="{ row, column, $index }">
							<IStates :row="row" :states="states" :prop="(prop as string)"></IStates>
						</template>
						<template v-if="dateFormat" #default="{ row, column, $index }">
							{{ getDataStr(dateFormat, row[prop || '']) }}
						</template>

						<template v-if="slotName" v-slot="slotProps">
							<slot :name="slotName" v-bind="slotProps || {}"> </slot>
						</template>
						<template v-if="slotHeaderName" v-slot:header="slotProps">
							<slot :name="slotHeaderName" v-bind="slotProps || {}"> </slot>
						</template>
					</el-table-column>
				</template>
			</template>
			<template v-if="!defaultSlotIndex && $slots.default">
				<template v-for="item in $slots.default()">
					<component v-if="!item.props?.prop || !hideColumnKeys.includes(item.props.prop)" :is="item"></component>
					<!-- :show-overflow-tooltip="
							item.props?.showOverflowTooltip ?? item.props?.['show-overflow-tooltip'] ?? showOverflowTooltip
						" -->
				</template>
			</template>
			<el-table-column
				v-if="getBtns"
				:width="operateWidth"
				:label="operateOther?.label || '操作'"
				:prop="operateOther?.prop || 'operates'"
				:fixed="operateOther?.fixed || 'right'"
				v-bind="operateOther || {}"
			>
				<template #default="{ row, column, $index }">
					<i-btns :key="row" :btns="getBtns_(row, column)" :moreNumber="btnsMoreNumber" :data="row"></i-btns>
				</template>
			</el-table-column>

			<template v-slot:empty="slotProps">
				<slot name="empty" v-bind="slotProps || {}"></slot>
			</template>
			<template v-slot:append="slotProps">
				<slot name="append" v-bind="slotProps || {}"></slot>
			</template>
		</el-table>
		<el-pagination
			v-if="pagination_"
			ref="refPagination"
			class="t-pagination"
			v-bind="pagination_"
			@change="onPageChange"
			@current-change="onPageCurrentChange"
			@size-change="onPageSizeChange"
		/>
	</div>
</template>

<script lang="tsx" setup>
import { innerH } from '@/store';
import { TableInstance, ElTooltipProps } from 'element-plus';
import { onMounted, onUnmounted, ref, useSlots, watch } from 'vue';
import { RefreshRight, Search } from '@element-plus/icons-vue';
import ISetting from './setting.vue';
import type { StatesItem, TypeStatesColor } from './type';
import Moment from 'moment';
import IStates from './states.vue';
import { defaultFormatAll, defaultFormatDate, defaultFormatMonth, defaultFormatTime, defaultText } from '@/config';
defineOptions({ inheritAttrs: false });
interface Props extends /* @vue-ignore */ ElTableProps {
	// interface Props {
	hideHeader?: boolean;
	columns?: CommonTableColumn<any>[];
	border?: boolean;
	pagination?: ElPaginationProps | boolean; // 是否开启分页
	total?: number; //	总条目数
	pageSize?: number; //每页显示条目个数
	pageNumber?: number; // 当前页数  key可以根据后台定义的字段改
	loading?: undefined | boolean;
	showIndex?: boolean;
	showCheckBox?: boolean;
	selectable?: any;
	height?: string | number; // Table 的高度， 默认为自动高度。 如果 height 为 number 类型，单位 px；如果 height 为 string 类型，则这个高度会设置为 Table 的 style.height 的值，Table 的高度会受控于外部样式。
	fillScreen?: boolean; // 是否充满整个屏幕
	otherHeight?: number; // 首屏中除了表格以外其它内容的高度 首屏撑满的表格可以用这个 表格高度为：首屏高度 - otherHeight
	getBtns?: OptionBtn[] | ((row: any, column: CommonTableColumn<any>) => OptionBtn[]); // 操作按钮 列中的按钮
	operateWidth?: string | number | undefined; // 操作按钮列的宽度
	operateOther?: ElTableColumnProps; // 操作按钮列的其它参数
	getList?: (val: CommonTableParams) => Promise<any>;
	defaultSlotIndex?: boolean; // 默认插入的插槽的位置 true 在 columns 之前 false  在columns 之后
	soleKey?: string; // 唯一的key  ISetting 组件中使用
	btnsMoreNumber?: number; // i-btns 组件中的参数 按钮超过多少个后，多出的按钮放到 ... 中
	showOverflowTooltip?: boolean | Partial<ElTooltipProps>; // 全局默认 的
}
const props = withDefaults(defineProps<Props>(), {
	hideHeader: false,
	showIndex: true,
	border: true,
	// pageNumber: 1,
	// pageSize: 20,
	operateWidth: 150,
	fillScreen: true,
	loading: undefined,
	pagination: undefined,
	defaultSlotIndex: false, // 默认在 columns 之后插入
	btnsMoreNumber: 5,
	showOverflowTooltip: () => ({
		showAfter: 1000,
	}), // 全局默认 的
});
const emit = defineEmits<{
	(ev: 'update:value', value: string): void;
	(ev: 'update:pageSize', value: number): void;
	(ev: 'update:pageNumber', value: number): void;
	(ev: 'pageCurrentChange', val: number): void; // current-page 改变时触发
	(ev: 'pageSizeChange', val: number): void; //  page-size 改变时触发
	(ev: 'pageChange', pageNumber: number, pageSize: number): void; // 对应 element-plus Pagination 组件中的 change 事件 	current-page 或 page-size 更改时触发
	(ev: 'filter', all: any): void; // 搜索，筛选，排序时都会触发这个事件
	(ev: 'search', val: any): void; // 搜索 触发这个时间
	(ev: 'refresh', all: any): void; //
	(ev: 'sortChange', data: { column: any; prop: string; order: any }): void; //
	(ev: 'filterChange', newFilters: any): void; //
}>();
const slots = useSlots();
const refTable = ref<TableInstance>();
const tableSize = ref(props.size); //  'large' | 'default' | 'small'
const hideColumnKeys = ref<string[]>([]); // 隐藏的column的 keys 之所以用隐藏的key 是因为判断起来更稳妥 新增的列也不用重新
const columnOptions = ref<OptionItem[]>([]);
const mapColumnSearch = ref<Record<string, any>>({}); // 储存 搜索框 的输入值
let mapSearchKey = {}; // 如果用户指定了传入接口的值，优先使用这个值，默认使用列的 prop
const mapColumnFilter = ref<Record<string, any[]>>({}); // 储存 筛选框 的值
const mapColumnSort = ref<Record<string, any>>({}); // 储存 排序 的值
const refDomBox = ref<HTMLDivElement>();
const refHeader = ref<HTMLDivElement>();
const refPagination = ref();
const loading_ = ref(props.loading || false);
const height_ = ref<number | undefined | string>(undefined);

const pageSize_ = 20; // 默认的
// 默认的分页器参数
const defaultPagination: ElPaginationProps = {
	// small: true,
	size: 'small',
	layout: 'prev, pager, next, jumper, ->, total',
	background: true,
	pageSizes: [20, 50, 100],
};

// 分页器的一些参数
const pagination_ = ref<ElPaginationProps | null>(null);

// 'red' | 'blue' | 'yellow' | 'green' | 'gray'
const mapColor: Record<TypeStatesColor, string> = {
	red: 'var(--el-color-error)',
	blue: 'var(--el-color-primary)',
	yellow: 'var(--el-color-warning)',
	green: 'var(--el-color-success)',
	gray: 'var(--el-color-info)',
};

watch(
	[() => props.height, () => props.otherHeight, innerH],
	() => {
		if (props.height) {
			height_.value = props.height;
		} else if (props.otherHeight) {
			height_.value = innerH.value - props.otherHeight;
		} else {
			height_.value = undefined;
		}
	},
	{ immediate: true },
);
watch(
	() => props.loading,
	(val) => {
		loading_.value = val || false;
	},
);
watch(
	() => props.columns,
	(list) => {
		const map = {};
		list?.forEach((item) => {
			(item as any).filterPlacement = item.filterPlacement || item['filter-placement'] || 'bottom-end';
			(item as any).columnKey = item.columnKey || item.prop;
			item.key_ = item.key || item.columnKey || item.label || '';
			if (item.prop) {
				// 没有prop的，不放到选项中
				const obj = columnOptions.value.find((item_) => item_.value === item.prop);
				if (!obj) {
					columnOptions.value.push({
						value: item.prop as string,
						label: item.label || '',
					});
				}
				if (item.searchable) {
					map[item.prop] = map[item.prop] || '';
					// 如果指定了 searchKey 优先用 searchKey
					mapSearchKey[item.prop] = item.searchKey || item.prop;
				}
			}
		});
		mapColumnSearch.value = map;
	},
	{ deep: true, immediate: true },
);
// watch(
// 	[() => props.showCheckBox, () => props.selectable],
// 	([val1, val2]) => {
// 		if (val1 || val2) {
// 			const obj = columnOptions.value.find((item) => item.value === '_select_');
// 			if (obj) return true; // 已经存在了，就不添加进去了
// 			columnOptions.value.unshift({
// 				value: '_select_',
// 				label: '选择列',
// 			});
// 		}
// 	},
// 	{ immediate: true },
// );
watch(
	() => props.showIndex,
	(val) => {
		if (val) {
			const obj = columnOptions.value.find((item) => item.value === '_index_');
			if (obj) return true; // 已经存在了，就不添加进去了
			columnOptions.value.unshift({
				value: '_index_',
				label: '序号',
			});
		}
	},
	{ immediate: true },
);
function onChangeSetting(obj) {
	hideColumnKeys.value = obj.hideColumnKeys;
	tableSize.value = obj.tableSize;
}
// 创建一个观察器实例，并将回调函数传递给它

let timer1;
const resizeOb = new ResizeObserver(() => {
	clearTimeout(timer1);
	timer1 = setTimeout(setHeight, 50);
});
onMounted(() => {
	// setHeight();
	if (props.fillScreen) {
		// resizeOb.observe(refDomBox.value!);
	}
	if (slots.default) {
		// el-table-column 使用时不传name 所以属于默认插槽
		const list = slots.default() || [];
		list.forEach((item) => {
			if (item.props) {
				const props_ = item.props;
				// 存在prop属性 label为表头名称
				if (props_.prop) {
					const obj = columnOptions.value.find((item_) => item_.value === props_.prop);
					if (!obj) {
						columnOptions.value.push({
							value: props_.prop,
							label: props_.label,
						});
					}
				}
			}
		});
	}
});
onUnmounted(() => {
	resizeOb.disconnect();
});
function setHeight() {
	if (!refDomBox.value) return;
	let h = refDomBox.value!.offsetTop; // 到页面顶部的高度
	let pDom = refDomBox.value!.offsetParent as HTMLDivElement;
	while (pDom) {
		h += pDom.offsetTop;
		pDom = pDom.offsetParent as HTMLDivElement;
	}
	if (refHeader.value) {
		h += refHeader.value.offsetHeight;
		h += 8; // margin-bottom
	}
	if (pagination_.value && refPagination.value) {
		h += refPagination.value.$el.offsetHeight;
		h += 16; // margin-top
	}
	console.log('111111111111 setHeight', h);
	height_.value = window.innerHeight - h - 32; // 减去下边距
}

function getBtns_(row: any, column: any) {
	// console.log('111111111111111111111111 getBtns_');
	if (typeof props.getBtns === 'function') {
		return props.getBtns(row, column);
	}
	return props.getBtns || [];
}
let timer2;
function emitFilter() {
	clearTimeout(timer2);
	timer2 = setTimeout(() => {
		const param = getAllFilter();
		console.log('111111111111 props.loading', props.loading);
		refreshList(param);
		emit('filter', param);
	}, 10);
}

function renderIndex(index: number) {
	if (!pagination_.value) {
		return index + 1;
	}
	return (pagination_.value.currentPage! - 1) * pagination_.value.pageSize! + index + 1;
}
function onPageCurrentChange(val: number) {
	if (pagination_.value) {
		pagination_.value.currentPage = val;
	}
	emit('update:pageNumber', val);
	emit('pageCurrentChange', val);
	// 在 onPageChange 中触发 emitFilter
}
function onPageSizeChange(val: number) {
	if (pagination_.value) {
		pagination_.value.pageSize = val;
	}
	emit('update:pageSize', val);
	emit('pageSizeChange', val);
	// 在 onPageChange 中触发 emitFilter
}
function onPageChange(pageNumber: number, pageSize: number) {
	if (pagination_.value) {
		pagination_.value.currentPage = pageNumber;
		pagination_.value.pageSize = pageSize;
	}
	emit('update:pageNumber', pageNumber);
	emit('update:pageSize', pageSize);
	emit('pageChange', pageNumber, pageSize);
	emitFilter();
}

watch(
	() => props.pagination,
	(val) => {
		console.log('1111111111 触发了监听1 props.pagination');
		// 为false时，说明用户明确指出，不使用分页
		if (props.pagination === false) {
			pagination_.value = null;
		} else if (typeof props.pagination === 'boolean') {
			pagination_.value = { ...defaultPagination };
		} else if (typeof props.pagination === 'object') {
			pagination_.value = {
				...defaultPagination,
				...props.pagination,
			};
		}
	},
	{ immediate: true },
);
watch(
	[() => props.pageNumber, () => props.total, () => props.pageSize],
	() => {
		console.log('1111111111 触发了监听2 ', props.pagination, props.pageNumber, props.total, props.pageSize);
		// 为false时，说明用户明确指出，不使用分页
		if (props.pagination === false) return;
		if (props.pageNumber !== undefined || props.total !== undefined || props.pageSize !== undefined) {
			pagination_.value = pagination_.value || { ...defaultPagination };
			pagination_.value.total = props.total ?? pagination_.value.total;
			pagination_.value.pageSize = props.pageSize ?? (pagination_.value.pageSize || pageSize_);
			pagination_.value.currentPage = props.pageNumber ?? (pagination_.value.currentPage || 1);
		}
	},
	{ immediate: true },
);

// function getRowKey(row:any){
// 	if(props.rowKey){
// 		if(typeof props.rowKey==='string')return row[props.rowKey]
// 		return
// 	}
// }

function onSearchInput(column: any) {
	console.log('11111', column, mapColumnSearch.value);
	if (!column.searchable) return;
	if (column.searchable === 'custom') {
		// 自定义时 使用后台搜索
		emit('search', {
			...column,
			keyword: mapColumnSearch.value[column.prop],
			...mapColumnSearch.value,
		});
		emitFilter();

		return;
	}
	// 其它情况 使用前端搜索，目前没这个需求，暂时使用后台搜索
}

function onSortChange(data: { column: any; prop: string; order: any }) {
	console.log('1111111111111111', data);
	emit('sortChange', data);
	if (!data.prop) return;
	for (const key in mapColumnSort.value) {
		mapColumnSort.value[key] = '';
	}
	// ['ascending', 'descending', null]
	mapColumnSort.value[data.prop] = data.order;
	emitFilter();
}
function onFilterChange(newFilters: any) {
	console.log('1111111111111111', newFilters);
	emit('filterChange', newFilters);
	for (const key in newFilters) {
		mapColumnFilter.value[key] = newFilters[key];
	}
	emitFilter();
}

function onRefresh() {
	// console.log('11111', refTable.value! );
	// console.log('11111', slots.default());
	resetList();
	emit('refresh', getAllFilter());
	// emitFilter();
}
function getAllFilter() {
	const param_ = Object.keys(mapColumnSearch.value).reduce((a, prop) => {
		let prop_ = mapSearchKey[prop] || prop;
		a[prop_] = mapColumnSearch.value[prop];
		return a;
	}, {});
	const obj: CommonTableParams = {
		// search: { ...mapColumnSearch.value },
		// filter: { ...mapColumnFilter.value },
		// sort: { ...mapColumnSort.value },
		pageNumber: pagination_.value?.currentPage || 0, // 当前页码
		pageSize: pagination_.value?.pageSize || pageSize_, // 每页多少个
		...param_,
	};
	for (const key in mapColumnFilter.value) {
		obj[key] = mapColumnFilter.value[key].join(',');
	}
	for (const key in mapColumnSort.value) {
		if (mapColumnSort.value[key]) {
			//应后端的要求，排序字段在传参的时候，要求前端将排序字段由驼峰转换成小写+下划线
			function camelToUnderscore(str) {
				return str.replace(/([A-Z])/g, '_$1').toLowerCase();
			}
			obj.orderBy = camelToUnderscore(key);
			obj.ascOrDesc = mapColumnSort.value[key] === 'descending' ? 'desc' : 'asc';
			// obj[key] = mapColumnSort.value[key] === 'descending' ? 2 : 1;
		}
	}
	// console.log('111111111111111111111', obj);
	return obj;
}
function checkHide(hide: CommonTableColumn['hide']) {
	if (typeof hide === 'boolean') return hide;
	if (typeof hide === 'function') return hide();
	return false;
}

function clearSearch(keys?: string[]) {
	keys = keys || Object.keys(mapColumnSearch.value);
	keys.forEach((key) => {
		mapColumnSearch.value[key] = '';
	});
	emit('search', { keyword: '', ...mapColumnSearch.value });
	emitFilter();
}
function clearSort() {
	let keys = Object.keys(mapColumnSort.value);
	keys.forEach((key) => {
		mapColumnSort.value[key] = '';
	});
	emit('sortChange', { column: {}, prop: '', order: '' });
	emitFilter();
	return refTable.value?.clearSort();
}
function clearFilter(columnKeys?: string[] | undefined) {
	let keys = columnKeys || Object.keys(mapColumnFilter.value);
	keys.forEach((key) => {
		mapColumnFilter.value[key] = [];
	});

	emit('filterChange', {});
	emitFilter();
	return refTable.value?.clearFilter(...arguments);
}

// const defaultFormatMonth = 'YYYY-MM'; // 默认的时间格式 日期
// const defaultFormatDate = 'YYYY-MM-DD'; // 默认的时间格式 日期
// const defaultFormatTime = 'HH:mm:ss'; // 默认的时间格式 时间
// const defaultFormatAll = 'YYYY-MM-DD HH:mm:ss'; // 默认的时间格式 日期带时间
const mapDateFormat = {
	month: defaultFormatMonth,
	date: defaultFormatDate,
	time: defaultFormatTime,
	all: defaultFormatAll,
};
function getDataStr(dateFormat: CommonTableColumn['dateFormat'] = '', val?: string | Date | number) {
	if (!val) return defaultText;
	if (dateFormat === true) return Moment(val).format(defaultFormatDate);
	if (dateFormat === false) return val;
	return Moment(val).format(mapDateFormat[dateFormat] || defaultFormatDate);
}
function refreshList(param?: CommonTableParams) {
	if (typeof props.getList === 'function') {
		// 如果外部明确表示 loading 为false 则不显示loading
		loading_.value = props.loading === false ? false : true;
		return props.getList(param || getAllFilter()).finally(() => {
			loading_.value = false;
		});
	}
	return Promise.reject('不存在 props.getList');
}

function getRef() {
	return refTable.value!;
}
// 通过 handle 方法 调用 el-table上的方法，
function handle() {
	if (refTable.value) {
		const [type, ...other] = arguments;
		if (type === 'clearSort') {
			clearSort();
		} else if (type === 'clearFilter') {
			clearFilter(...other);
		} else if (typeof refTable.value[type] === 'function') {
			return refTable.value[type](...other);
		} else {
			throw new TypeError(`el-table不存在 ${type} 方法`);
		}
	} else {
		throw new TypeError(`el-table不存在`);
	}
}
// 重置所有 包括筛选条件 页码
function resetList() {
	clearSearch();
	clearSort();
	clearFilter();
	onPageChange(1, pagination_.value?.pageSize || pageSize_);
}
defineExpose({
	handle: handle as Function,
	getRef: getRef,
	clearSearch: clearSearch,
	clearSort: clearSort,
	clearFilter: clearFilter,
	refreshList: refreshList,
	resetList: resetList,
});
</script>
<style lang="scss" scoped>
.i-common-table-box {
	width: 100%;
	box-sizing: border-box;
	:deep(.el-table__column-filter-trigger) {
		margin-left: 4px;
	}
}

.t-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-shrink: 0;
	min-height: 24px;
	margin-bottom: 8px;
	position: relative;
	.t-header-box {
		display: flex;
		align-items: center;
		flex-grow: 1;
		// 特殊，header下 tabs 的样式，
		> :deep(.el-tabs) {
			&:before {
				content: '';
				position: absolute;
				bottom: -1px;
				left: 0;
				right: 0;
				height: 2px;
				background-color: var(--el-border-color-light);
			}
		}
	}
}
.t-refresh-icon {
	flex-shrink: 0;
	cursor: pointer;
	justify-items: flex-end;
	margin-left: 20px;
}
:deep(.t-cell-index) {
	.cell {
		padding: 0;
	}
}
.t-pagination {
	margin-top: 16px;
	// padding-bottom: 4px;
	justify-content: flex-end;
	:deep(.el-pagination__rightwrapper) {
		flex-grow: 0;
	}
}
.t-search-header {
	display: inline-flex;
	align-items: center;
	justify-content: space-between;
}
.t-search-icon {
	margin-left: 4px;
	&.t-search {
		color: var(--el-color-primary);
	}
}
</style>
