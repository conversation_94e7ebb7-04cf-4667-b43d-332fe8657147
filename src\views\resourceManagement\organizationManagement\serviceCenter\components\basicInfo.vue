<template>
	<el-dialog
		v-model="dialogVisible"
		width="1000px"
		title="基本情况"
		:close-on-click-modal="false"
		append-to-body
		align-center
		destroy-on-close
		@closed="closeDialog"
		@open="handleOpen"
	>
		<el-tabs v-model="activeTab" type="card" class="demo-tabs" @tab-click="handleClick">
			<el-tab-pane label="人员信息" name="first">
				<el-form ref="formRef" :model="formData" label-width="85px">
					<el-row :gutter="24">
						<el-col :span="8">
							<el-form-item label="人员姓名" prop="userName">
								<el-input placeholder="请输入人员姓名" v-model="formData.userName"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="8">
							<el-form-item label="人员手机号" prop="userTelephone">
								<el-input placeholder="请输入人员手机号" v-model="formData.userTelephone"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-button type="primary" @click="clickGetList">查询</el-button>
							<el-button @click="clickResetList">重置</el-button>
						</el-col>
					</el-row>
				</el-form>
				<common-table
					:hide-header="true"
					:height="580"
					ref="refTable"
					:columns="columnsUser"
					:getList="getList"
					:total="pageTotal"
					:data="tableData"
				>
				</common-table>
			</el-tab-pane>
			<el-tab-pane label="机具信息" name="second">
				<el-form ref="formRef2" :model="formData2" label-width="85px">
					<el-row :gutter="24">
						<el-col :span="16">
							<el-form-item label="服务类型" prop="serviceType">
								<el-select v-model="formData2.serviceType" placeholder="请选择服务类型">
									<el-option v-for="item in center_service_type" :key="item.code" :label="item.name" :value="item.code" />
								</el-select>
							</el-form-item>
						</el-col>

						<el-col :span="8">
							<el-button type="primary" @click="clickGetList2">查询</el-button>
							<el-button @click="clickResetList2">重置</el-button>
						</el-col>
					</el-row>
				</el-form>
				<common-table
					:hide-header="true"
					:height="580"
					ref="refTable2"
					:columns="columnsUser2"
					:getList="getList2"
					:total="pageTotal2"
					:data="tableData2"
				>
				</common-table>
			</el-tab-pane>
		</el-tabs>
	</el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { CenterUserBaseVO, EquipmentBaseDTO, RequestSaveCenter, ServiceCenterBaseVO, getListRequestType } from '../api/types';
import { api_getUserList, api_getEquipmentList } from '../api';
import { TabsPaneContext } from 'element-plus';
import { setObjValue } from '@/utils';
import { api_getDict } from '@/api/dict';
import { nextTick } from 'vue';
interface Props {
	rowData: ServiceCenterBaseVO | null;
}
const props = withDefaults(defineProps<Props>(), {});
const dialogVisible = defineModel<boolean>('visible');

const emits = defineEmits<{}>();
const activeTab = ref<string>('first');
const handleClick = (tab: TabsPaneContext, event: Event) => {
	console.log(tab, event);
};

const defaultData: any = {
	userName: '',
	userTelephone: '',
};

const formData = ref<any>(Object.assign({}, defaultData, { subsidyRule: [] }));

const columnsUser: CommonTableColumn<CenterUserBaseVO>[] = [
	{
		label: '人员姓名',
		prop: 'sysUserName',
		minWidth: 210,
	},
	{
		label: '人员手机号',
		prop: 'sysUserTelephone',
	},
];
const refTable = ref<CommonTableInstance>();
const tableData = ref<CenterUserBaseVO[]>([]);
const pageTotal = ref(0);
function getList(param?: CommonTableParams) {
	let data = {
		pageNo: param?.pageNumber || 1,
		pageSize: param?.pageSize || 20,
		userName: formData.value.userName,
		userTelephone: formData.value.userTelephone,
		centerUuid: props.rowData!.tenantId,
	};
	return api_getUserList(data).then((info) => {
		tableData.value = info.list;
		pageTotal.value = info.total;
	});
}

const defaultData2: any = {
	serviceType: '',
};

const formData2 = ref<any>(Object.assign({}, defaultData2, {}));

const center_service_type = ref<DicDataItemCode[]>([]);
const columnsUser2: CommonTableColumn<EquipmentBaseDTO>[] = [
	{
		label: '林机类型',
		prop: 'equipmentCode',
	},
	{
		label: '服务类型',
		prop: 'jobType',
		formatter(row, column, cellValue, index) {
			let str = '';
			let typeValue = cellValue.split(',');
			typeValue.forEach((r: string, index: number) => {
				center_service_type.value.forEach((d) => {
					if (d.code == r) str += d.name;
					if (index != typeValue.length - 1 && d.code == r) str += '、';
				});
			});
			return str;
		},
	},
	{
		label: '所属服务队',
		prop: 'ownerName',
	},
];
const refTable2 = ref<CommonTableInstance>();
const tableData2 = ref<EquipmentBaseDTO[]>([]);
const pageTotal2 = ref(0);
function getList2(param?: CommonTableParams) {
	let data = {
		pageNo: param?.pageNumber || 1,
		pageSize: param?.pageSize || 20,
		serviceType: formData2.value.serviceType,
		centerUuid: props.rowData!.tenantId,
	};
	return api_getEquipmentList(data).then((info) => {
		tableData2.value = info.list;
		pageTotal2.value = info.total;
	});
}
onMounted(() => {});

function handleOpen() {
	api_getDict('center_service_type').then((res) => {
		center_service_type.value = res;
	});
	refTable.value?.refreshList();
	refTable2.value?.refreshList();
}
function closeDialog() {
	dialogVisible.value = false;
	setObjValue(formData.value, {}, defaultData);
}

function clickGetList() {
	refTable.value?.refreshList();
}

function clickResetList() {
	formData.value.userName = '';
	formData.value.userTelephone = '';
	refTable.value?.refreshList();
}
function clickGetList2() {
	refTable2.value?.refreshList();
}

function clickResetList2() {
	formData2.value.serviceType = '';
	refTable2.value?.refreshList();
}
</script>
<style lang="scss" scoped>
.demo-tabs {
	:deep(.el-tabs__header) {
		border-bottom: none;
	}
	:deep(.el-tabs__nav) {
		border-bottom: 1px solid var(--el-border-color-light);
	}
}
</style>
