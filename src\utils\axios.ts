// axios配置  可自行根据项目进行更改，只需更改该文件即可，其他文件可以不动

import { createVNode } from 'vue';
import { getLocalToken, getTokenPromise, setTokenLocal } from '@/utils/token';
import { IAxios, EnumContentType } from './class/axios';
import type { Result } from './class/axios';
import { isString } from '@vue/shared';
export { EnumContentType, Result };
import { setUrlParam } from '@/utils';
import { AxiosRequestConfig, AxiosResponse, Method } from 'axios';
import { isDev } from '@/config';
import { ElMessage, ElMessageBox } from 'element-plus';
import { goToLogin } from '@/api/user';

export enum EnumAim {
	test = '/test_api', // 测试地址 - 内网
	pro = '/pro_api', // 正式地址
	aim_jt = '/jt_api', // 马哥 的地址
	aim_idaas = '/idaas_api', // idaas-service 的地址
}

// 全局的
let globalAim: EnumAim | '' = '';
// globalAim = EnumAim.pro; // 仅本地开发时调用本行
globalAim = EnumAim.test; // 仅本地开发时调用本行
// globalAim = EnumAim.aim_jt; // 仅本地开发时调用本行

const resultCode = {
	success: 0,
	token_out: 20301, // Token过期
	not_login: 20200, // 用户未登录
	token_error: 20300, // 无效Token
	token_lose: 20302, // Token缺失
};

type ResultCodeCustom = typeof resultCode;

const Axios = new IAxios<EnumAim, ResultCodeCustom>(
	{
		timeout: 0,
		// 数据处理方式
		withCredentials: true,
	},
	{
		tokenKey: 'Authorization',
		isDev: isDev,
		globalAim: globalAim,
		defaultAim: EnumAim.test,
		resultCode: resultCode,
		getToken: () =>
			getTokenPromise().catch((err) => {
				goToLogin();
				return Promise.reject(err);
			}),
		onFulfilled: onFulfilled,
		messageSuccess(text: string) {
			ElMessage.success(text);
		},
		messageError(msg: string) {
			return ElMessageBox({
				type: 'error',
				title: '错误提示',
				showClose: false,
				// 后台返回的错误信息可能需要换行，换行符为 \n 这里替换成 br
				message: createVNode('div', { innerHTML: msg.replace('\n', '<br/>') }),
			});
		},
		showLoading() {
			G_loading.value = true;
		},
		hideLoading() {
			G_loading.value = false;
		},
	},
);

function onFulfilled(conf: AxiosRequestConfig, rel: AxiosResponse<Result>): Promise<any> {
	// console.log('1111111111 onFulfilled', rel);
	// if (isDev) return Promise.resolve(rel);
	if (rel.data?.code === resultCode.token_out) {
		// 登录超时
		setTokenLocal();
		goToLogin();
		return Promise.reject(rel.data);
	}
	return Promise.resolve(rel);
}
export default Axios;

// 模拟返回成功的方法，供开发测试时使用
export function resolveFunc<T = any>(rel: any = '操作成功', timeout?: number): Promise<T> {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			resolve(rel);
		}, timeout || 100);
	});
}
// 模拟返回失败的方法，供开发测试时使用
export function rejectFunc<T = any>(rel: any = '操作失败', timeout?: number): Promise<T> {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			reject(rel);
		}, timeout || 100);
	});
}

interface XMLHttpOptions<TEnumAim = string> {
	method?: Method;
	responseType?: XMLHttpRequestResponseType;
	data?: any;
	params?: any;
	tokenKey: string;
	contentType?: string;
	aim?: TEnumAim;
	fullPath?: boolean;
	ignoreGlobalAim?: boolean;
	toStr?: boolean;
	loading?: boolean;
	errorTips?: boolean | string;
	successTips?: boolean | string;
	onprogress?: (event: ProgressEvent<EventTarget>) => void;
}
// 原生 XMLHttpRequest
export function XMLHttp<TEnumAim>(url: string, options: XMLHttpOptions<TEnumAim>) {
	const { loading, errorTips, successTips } = options;
	loading && (G_loading.value = true);
	return XMLHttp2(url, options)
		.then((xhr) => {
			return responseNotJson(xhr).catch((err) => {
				if (err?.code === resultCode.token_out) {
					// 登录超时
					setTokenLocal();
					goToLogin();
				}
				return Promise.reject(err);
			});
		})
		.then((xhr) => {
			if (successTips) {
				const msg = isString(successTips) ? successTips : '成功！';
				Axios.messageSuccess(msg);
			}
			return xhr;
		})
		.catch((err) => {
			if (errorTips) {
				const msg = isString(errorTips) ? errorTips : err?.message || '失败！';
				Axios.messageError(msg);
			}
			return Promise.reject(err);
		})
		.finally(() => {
			loading && (G_loading.value = false);
		});
}
function XMLHttp2<TEnumAim>(url: string, options: XMLHttpOptions<TEnumAim>): Promise<XMLHttpRequest> {
	return new Promise((resolve, reject) => {
		let { method, responseType, params, data, tokenKey, contentType, ignoreGlobalAim, fullPath, onprogress, toStr, aim } = options;
		method = method || 'GET';
		if (!fullPath) {
			url = Axios.getRootUrl(url, {
				aim: aim as any,
				ignoreGlobalAim,
			});
		}
		const xhr = new XMLHttpRequest();
		if (method === 'GET' || method === 'get') {
			url += setUrlParam(params || data);
		}
		xhr.open(method!, url);
		if (contentType) {
			xhr.setRequestHeader('Content-type', contentType);
		}
		if (tokenKey) {
			xhr.setRequestHeader(tokenKey, getLocalToken()!);
		}

		xhr.responseType = responseType!;
		if (onprogress) {
			xhr.onprogress = onprogress;
		}
		xhr.onload = function () {
			if (xhr.readyState === 4 && xhr.status === 200) {
				resolve(xhr);
			} else {
				reject(getRejectObj(''));
			}
		};
		xhr.onabort = function () {
			console.log('11111111 onabort', xhr);
			reject(getRejectObj('请求中断'));
		};
		xhr.onerror = function () {
			console.log('11111111 onerror');
			reject(getRejectObj('请求失败'));
		};
		xhr.ontimeout = function () {
			console.log('11111111 ontimeout');
			reject(getRejectObj('请求超时'));
		};
		if (method === 'GET' || method === 'get') {
			xhr.send();
		} else {
			if (toStr) {
				xhr.send(JSON.stringify(data));
			} else {
				xhr.send(data);
			}
		}
	});
}
interface XMLHttpDownloadOptions {
	method?: Method;
	fileName?: string;
	params?: any;
	data?: any;
	tokenKey?: string;
	fullPath?: boolean;
	contentType?: string;
	errorTips?: boolean;
	loading?: boolean;
	toStr?: boolean;
	onprogress?: (event: ProgressEvent<EventTarget>) => void;
}
// 原生下载功能， 可以触发下载进度的回调方法
export function XMLHttpDownload(url: string, options: XMLHttpDownloadOptions) {
	const { fileName, tokenKey = 'Token', errorTips = true, ...other } = options;
	return XMLHttp(url, {
		responseType: 'blob',
		tokenKey: tokenKey,
		errorTips: errorTips,
		...other,
	}).then((xhr) => {
		console.log('文件下载成功了');
		return responseToUrl(xhr.response).then((url) => {
			const fileName_ = fileName || getFileNameByXHR(xhr);
			saveFileByA(url, fileName_);
			return url;
		});
	});
}
// 原生下载功能， 可以触发下载进度的回调方法   农机券兑付下载功能，将token换成Authorizationdon
export function XMLHttpDownload2(url: string, options: XMLHttpDownloadOptions) {
	const { fileName, tokenKey = 'Authorization', errorTips = true, ...other } = options;
	return XMLHttp(url, {
		responseType: 'blob',
		tokenKey: tokenKey,
		errorTips: errorTips,
		...other,
	}).then((xhr) => {
		console.log('文件下载成功了');
		return responseToUrl(xhr.response).then((url) => {
			const fileName_ = fileName || getFileNameByXHR(xhr);
			saveFileByA(url, fileName_);
			return url;
		});
	});
}

// 将 xhr.response 转换成 url
export function responseToUrl(response: XMLHttpRequest['response'], blobType?: string): Promise<string> {
	return new Promise((resolve, reject) => {
		if (typeof response.type === 'string' && response.type.includes('application/json')) {
			// 返回json的话，说明下载失败了
			// 将blob对象转为json
			var reader = new FileReader();
			reader.onload = (e) => {
				let result;
				try {
					result = JSON.parse(e.target?.result as string) || {};
				} catch (error) {
					console.log('responseToUrl error', error);
				}
				reject(result);
			};
			reader.readAsText(response);
		} else if (typeof response.code === 'number') {
			reject(response);
		} else {
			const param: BlobPropertyBag = {};
			if (blobType) {
				param.type = blobType;
			}
			const data = new Blob([response], param);
			const url = window.URL.createObjectURL(data);
			resolve(url);
		}
	});
}
/**
 * 获取 XMLHttpRequest 中的文件名
 */
export function getFileNameByXHR(xhr: XMLHttpRequest) {
	let fileName_ = '文件';
	const disposition = xhr.getResponseHeader('Content-Disposition');
	if (disposition) {
		fileName_ = decodeURIComponent(disposition.split('=')[1]) || '文件';
	}
	return fileName_;
}
/**
 * 通过a标签保存文件路径到本地
 */
export function saveFileByA(url: string, fileName: string) {
	const a = document.createElement('a');
	a.download = fileName;
	a.href = url;
	a.onload = function (ev) {
		console.log('a.onload ', ev);
		// 经过测试，这个事件不会触发 先放这里
		window.URL.revokeObjectURL(url);
	};
	a.click();
}

function getRejectObj(msg: string, code: number = -1) {
	return {
		code: code,
		message: msg || '请求失败',
	};
}
// 判断返回值是不是 json
function responseNotJson(xhr: XMLHttpRequest): Promise<XMLHttpRequest> {
	const response = xhr.response;
	if (typeof response.type === 'string' && response.type.includes('application/json')) {
		return new Promise((resolve, reject) => {
			// 返回json的话，说明下载失败了
			// 将blob对象转为json
			var reader = new FileReader();
			reader.onload = (e) => {
				let result;
				try {
					result = JSON.parse(e.target?.result as string) || {};
				} catch (error) {
					console.log('responseToUrl error', error);
				}
				reject(result);
			};
			reader.readAsText(response);
		});
	} else {
		return Promise.resolve(xhr);
	}
}
