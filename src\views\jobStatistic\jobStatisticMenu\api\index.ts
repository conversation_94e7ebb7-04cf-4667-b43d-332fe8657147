import http, { resolveFunc, rejectFunc, EnumAim, XMLHttpDownload2, EnumContentType } from '@/utils/axios';
import * as Types from './types';
import { isDev } from '@/config';
const aim = EnumAim.test;

export enum Api {
	getList = '/web-api/job-statistics/v1/page', //  分页查询表格数据
	saveCenter = '/web-api/job-statistics/v1/save', //  保存服务队人员
	editCenter = '/web-api/job-statistics/v1/modify', //  修改服务队人员
	deleteCenter = '/web-api/job-statistics/v1/', //  删除服务队人员
}
export function api_getList(param: Types.getListRequestType): Promise<Types.PageResultJobStatisticsBaseVO> {
	const url = Api.getList;
	return http.get(url, param, { aim, loading: true }).then((res) => res[0] || {});
}

export function api_saveCenter(param: Types.RequestSave): Promise<any> {
	const url = Api.saveCenter;
	return http.post(url, param, { aim, successTips: true, loading: true });
}

export function api_editCenter(param: Types.RequestSave): Promise<any> {
	const url = Api.editCenter;
	return http.post(url, param, { aim, successTips: true, loading: true });
}

export function api_delete(serviceUuid: string) {
	const url = Api.deleteCenter + serviceUuid;
	return http.delete(url, null, { aim, successTips: false, loading: true });
}
