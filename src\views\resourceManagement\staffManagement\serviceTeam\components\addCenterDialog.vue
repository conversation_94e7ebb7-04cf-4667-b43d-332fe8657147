<template>
	<el-dialog
		v-model="dialogVisible"
		width="550px"
		:title="title"
		:close-on-click-modal="false"
		append-to-body
		align-center
		destroy-on-close
		@closed="closeDialog"
		@open="handleOpen"
	>
		<el-form
			ref="formRef"
			style="margin: 0 12px"
			:model="formData"
			:disabled="type == 'view'"
			label-width="100px"
			:rules="formRules"
		>
			<el-row :gutter="24">
				<el-col>
					<el-form-item label="行政区划" prop="address">
						<el-cascader
							:props="{
								value: 'areaId',
								label: 'areaName',
								children: 'children',
							}"
							placeholder="请选择行政区划"
							style="width: 100%"
							v-model="formData.address"
							:options="areas"
							@change="handleChange"
						/>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col>
					<el-form-item label="所属中心" prop="centerUuid">
						<el-select @change="handleChangeCenter" v-model="formData.centerUuid" placeholder="请选择所属服务中心">
							<el-option
								v-for="item in centerListOptions"
								:key="item.centerUuid"
								:label="item.centerName"
								:value="item.centerUuid"
							/>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col>
					<el-form-item label="所属服务队" prop="teamUuid">
						<el-select v-model="formData.teamUuid" placeholder="请选择所属服务队">
							<el-option v-for="item in teamListOptions" :key="item.teamUuid" :label="item.teamName" :value="item.teamUuid" />
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col>
					<el-form-item label="姓名" prop="userName">
						<el-input placeholder="请输入人员名称" v-model="formData.userName"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col>
					<el-form-item label="联系方式" prop="userTelephone">
						<el-input placeholder="请输入联系方式" v-model="formData.userTelephone"></el-input>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row :gutter="24">
				<el-col>
					<el-form-item label="备注" prop="remark">
						<el-input v-model="formData.remark" placeholder="请输入备注信息" type="textarea" :rows="2" resize="none" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<el-row justify="center">
			<el-button @click="closeDialog" style="margin-right: 12px">取消</el-button>
			<el-button type="primary" @click="submit" v-if="type !== 'view'" style="margin-right: 12px">确认</el-button>
		</el-row>
	</el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ElForm, FormRules } from 'element-plus';
import { RequestSaveCenter, CenterUserBaseVO, CenterList, TeamList } from '../api/types';
import { api_editCenter, api_getCenterList, api_getTeamList, api_saveCenter } from '../api';
import { watch } from 'vue';
import { setObjValue } from '@/utils';
import { api_getArea } from '@/api/dict';
import { storeUserInfo } from '@/store';
interface Props {
	type: string; //新增还是查看还是编辑
	rowData?: CenterUserBaseVO | null;
}
const props = withDefaults(defineProps<Props>(), {
	type: 'add',
});
const dialogVisible = defineModel<boolean>('visible');

const title = computed(() => {
	switch (props.type) {
		case 'add':
			return '新增';
		case 'view':
			return '查看';
		case 'edit':
			return '编辑';
		default:
			break;
	}
});
const emits = defineEmits<{
	(ev: 'saveForm'): void;
}>();

const centerListOptions = ref<CenterList[]>([]);
const teamListOptions = ref<TeamList[]>([]);
function fetchCenterList() {
	api_getCenterList({
		provinceCode: formData.value.address ? formData.value.address[0] : '',
		cityCode: formData.value.address ? (formData.value.address[1] ? formData.value.address[1] : '') : '',
		districtCode: formData.value.address ? (formData.value.address[2] ? formData.value.address[2] : '') : '',
		tenantId: storeUserInfo.enterpriseId,
	}).then((res) => {
		centerListOptions.value = res;
	});
}
onMounted(() => {});
const defaultData: RequestSaveCenter = {
	teamUuid: '', //中心uuid
	remark: '', //备注
	userName: '', //    用户名
	userTelephone: '', //    用户手机号

	centerUuid: '',
	address: [],
};

const formData = ref<RequestSaveCenter>(Object.assign({}, defaultData, { subsidyRule: [] }));
const rules: FormRules<RequestSaveCenter> = Object.keys(formData.value).reduce((acc, key) => {
	acc[key] = [
		{
			required: true,
			message: '不能为空',
			trigger: 'blur',
		},
	];
	return acc;
}, {} as FormRules);

const formRules = ref<FormRules<RequestSaveCenter>>(rules);
delete formRules.value.address;
delete formRules.value.centerUuid;
delete formRules.value.remark;
const formRef = ref<InstanceType<typeof ElForm>>();

async function handleOpen() {
	api_getArea().then((res) => {
		areas.value = res;
	});
	await fetchCenterList();
	setObjValue(formData.value, {}, defaultData);
	if (!dialogVisible.value || props.type == 'add') return;
	setObjValue(formData.value, props.rowData!, defaultData);
	formData.value.userName = props.rowData?.sysUserName;
	formData.value.teamUuid = props.rowData?.teamId;
	formData.value.centerUuid = props.rowData?.centerId;
	formData.value.address = [props.rowData?.provinceCode, props.rowData?.cityCode];
	if (props.rowData?.districtCode) formData.value.address.push(props.rowData?.districtCode);
	api_getTeamList({
		centerUuid: formData.value.centerUuid,
		tenantId: storeUserInfo.enterpriseId,
	}).then((res) => {
		teamListOptions.value = res;
	});
}
function submit() {
	formRef.value?.validate((valid) => {
		if (!valid) return;
		let param = {
			teamUuid: formData.value.teamUuid, //中心uuid
			remark: formData.value.remark, //备注
			userName: formData.value.userName, //    用户名
			userTelephone: formData.value.userTelephone,
		};
		let api_ = props.type == 'add' ? api_saveCenter : api_editCenter;
		if (props.type == 'edit') param['uuid'] = props.rowData!.uuid;
		api_(param).then((res) => {
			emits('saveForm');
			dialogVisible.value = false;
		});
	});
}

function closeDialog() {
	setObjValue(formData.value, {}, defaultData);
	dialogVisible.value = false;
	formRef.value!.clearValidate();
	centerListOptions.value = [];
	teamListOptions.value = [];
}

defineExpose({
	open,
	close,
});

const handleChange = (value) => {
	console.log(value);
	fetchCenterList();
};

function handleChangeCenter(value) {
	formData.value.teamUuid = '';
	api_getTeamList({
		centerUuid: value,
		tenantId: storeUserInfo.enterpriseId,
	}).then((res) => {
		teamListOptions.value = res;
	});
}

const areas = ref<AreaTreeBase[]>([]);
</script>
<style lang="scss" scoped>
.add-btn-t {
	width: 100%;
	padding: 12px 0;
	display: flex;
	justify-content: center;
	border-radius: 12px;
	background-color: #ecf5ff;
	margin-bottom: 12px;
	// border: 1px solid #f2f3f5;

	cursor: pointer;
	// color: #ecf5ff;
	// border: 1px solid #ecf5ff;
}
</style>
