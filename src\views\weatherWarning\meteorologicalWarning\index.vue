<template>
	<container-lcr ref="refContainer" :title="false" :topHeight="64">
		<template #top>
			<div style="width: 25%; height: 32px" class="form-b">
				<el-form class="g-form-box" ref="refForm" :model="formState" label-position="left">
					<el-form-item class="g-col-100" label="预警区域" prop="regionCode">
						<el-cascader
							placeholder="请选择预警区域"
							:props="cascaderProps"
							style="width: 100%"
							v-model="formState.regionCode"
							:options="areas"
							clearable
							@change="handleChange"
						/>
					</el-form-item>
				</el-form>
			</div>
			<div style="width: 25%; height: 32px" class="form-b">
				<el-form class="g-form-box" ref="refForm" :model="formState" label-position="left">
					<el-form-item class="g-col-100" label="预警类型" prop="jobType">
						<el-select v-model="formState.jobType" placeholder="请选择">
							<el-option v-for="item in center_service_type" :key="item.code" :label="item.name" :value="item.code" />
						</el-select>
					</el-form-item>
				</el-form>
			</div>
			<el-button type="primary" @click="clickSearchBtn">查询</el-button>
			<el-button type="" @click="clickResetBtn">重置</el-button>
		</template>
		<div class="table-box-wrap">
			<common-table
				:otherHeight="376"
				ref="refTable"
				:columns="columnsCommon"
				:getList="getList"
				:total="pageTotal"
				:data="tableData"
				:operateWidth="120"
				:get-btns="btns"
				:hide-header="true"
			>
			</common-table>
		</div>
		<AddCenterDialog
			:rowData="activeRow"
			:type="dialogType"
			v-model:visible="openBaseDialog"
			@saveForm="refreshTableData"
		></AddCenterDialog>
	</container-lcr>
</template>

<script lang="ts" setup>
import { cloneDeep } from 'lodash-es';
import { onMounted, ref } from 'vue';
import { api_getList } from './api/index';
import { JobBaseVO } from './api/types';
import AddCenterDialog from './components/addCenterDialog.vue';
import { api_getDict, api_getArea } from '@/api/dict';
const columnsCommon: CommonTableColumn<JobBaseVO>[] = [
	{
		label: '预警时间',
		prop: 'time',
	},
	{
		label: '预警类型',
		prop: 'type',
	},
	{
		label: '预警区域',
		prop: 'area',
	},
	{
		label: '预警等级',
		prop: 'level',
	},
	{
		label: '预警信息',
		prop: 'title',
	},
];
const tableData = ref<JobBaseVO[]>([]);

const pageTotal = ref(0);
const activeRow = ref<JobBaseVO | null>(null);
const btns: OptionBtn<JobBaseVO>[] = [
	{
		label: '查看',
		onClick(row) {
			dialogType.value = 'view';
			activeRow.value = row;
			openBaseDialog.value = true;
		},
	},
];

const refTable = ref<CommonTableInstance>();
function getList(param?: CommonTableParams) {
	let type = center_service_type.value.find((item) => item.code === formState.value.jobType)?.name || formState.value.jobTyp;
	let data = {
		pageNo: param?.pageNumber || 1,
		pageSize: param?.pageSize || 20,
		type: type, // 	* 名称
		regionCode: formState.value.regionCode,
	};
	return api_getList(data).then((info) => {
		tableData.value = info.alarm;
		pageTotal.value = info.total;
	});
}

const center_service_type = ref<DicDataItemCode[]>([]);
const areas = ref<AreaTreeBase[]>([]);
onMounted(() => {
	api_getDict('weatherAlarmType').then((res) => {
		center_service_type.value = res;
	});
	api_getArea().then((res) => {
		areas.value = res;
	});
	refTable.value?.refreshList();
});

// 级联选择器配置
const cascaderProps = ref({
	expandTrigger: 'click', // 点击展开
	checkStrictly: true, // 父子节点不关联，允许单独选择
	emitPath: false, // 只返回当前选中值，而非路径
	multiple: false, // 单选模式
	value: 'areaId',
	label: 'areaName',
	children: 'children',
});

// 预警区域改变
const handleChange = (value) => {
	console.log('value', value);

	formState.value.regionCode = value;
};
function clickSearchBtn() {
	refTable.value?.refreshList();
}
function clickResetBtn() {
	formState.value = cloneDeep(defaultForm);
	refTable.value?.refreshList();
}
const refContainer = ref<ContainerLcrInstance>();

interface quereType {
	jobType: string;
	regionCode: string;
}
const refForm = ref<ElFormInstance>();
const defaultForm: quereType = {
	jobType: '', // 	* 名称
	regionCode: '', // 预警区域
};
const formState = ref<quereType>(cloneDeep(defaultForm));

const openBaseDialog = ref<boolean>(false); //是否打开新增弹窗
const dialogType = ref('add');

function refreshTableData() {
	refTable.value?.refreshList();
}
</script>
<style lang="scss" scoped>
.query-box {
	display: flex;
	padding: 16px;
	box-sizing: border-box;
}
</style>
