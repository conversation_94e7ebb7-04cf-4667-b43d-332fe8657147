<template>
	<el-dialog
		v-model="dialogVisible"
		width="1000px"
		title="运行记录"
		:close-on-click-modal="false"
		append-to-body
		align-center
		destroy-on-close
		@closed="closeDialog"
		@open="handleOpen"
	>
	
				<el-form ref="formRef" :model="formData" label-width="85px">
					<el-row :gutter="24">
						<el-col :span="16">
							<el-form-item label="人员姓名" prop="userName">
								<el-input placeholder="请输入人员姓名" v-model="formData.userName"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-button type="primary" @click="clickGetList">查询</el-button>
							<el-button @click="clickResetList">重置</el-button>
						</el-col>
					</el-row>
				</el-form>
				<common-table
					:hide-header="true"
					:height="580"
					ref="refTable"
					:columns="columnsUser"
					:getList="getList"
					:total="pageTotal"
					:data="tableData"
				>
				</common-table>
			
	</el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { CenterUserBaseVO, ServiceCenterBaseVO } from '../api/types';
import { api_getUserList } from '../api';
import { setObjValue } from '@/utils';
interface Props {
	rowData: ServiceCenterBaseVO | null;
}
const props = withDefaults(defineProps<Props>(), {});
const dialogVisible = defineModel<boolean>('visible');

const emits = defineEmits<{}>();

const defaultData: any = {
	userName: '',
};

const formData = ref<any>(Object.assign({}, defaultData, { subsidyRule: [] }));

const columnsUser: CommonTableColumn<CenterUserBaseVO>[] = [
	{
		label: '人员姓名',
		prop: 'sysUserName',
		minWidth: 210,
	},
	{
		label: '人员手机号',
		prop: 'sysUserTelephone',
	},
];
const refTable = ref<CommonTableInstance>();
const tableData = ref<CenterUserBaseVO[]>([]);
const pageTotal = ref(0);
function getList(param?: CommonTableParams) {
	let data = {
		pageNo: param?.pageNumber || 1,
		pageSize: param?.pageSize || 20,
		userName: formData.value.userName,
		userTelephone: formData.value.userTelephone,
		centerUuid: props.rowData!.uuid,
	};
	return api_getUserList(data).then((info) => {
		tableData.value = info.list;
		pageTotal.value = info.total;
	});
}

onMounted(() => {});

function handleOpen() {
	refTable.value?.refreshList();
}
function closeDialog() {
	dialogVisible.value = false;
	setObjValue(formData.value, {}, defaultData);
}

function clickGetList() {
	refTable.value?.refreshList();
}

function clickResetList() {
	formData.value.userName = '';
	formData.value.userTelephone = '';
	refTable.value?.refreshList();
}

</script>
<style lang="scss" scoped>
.demo-tabs {
	:deep(.el-tabs__header) {
		border-bottom: none;
	}
	:deep(.el-tabs__nav) {
		border-bottom: 1px solid var(--el-border-color-light);
	}
}
</style>
