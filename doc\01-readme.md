## 项目概述
本项目是林机云系统的前端实现，主要提供以下功能：
- 资源管理
- 人员管理
- 机具管理
- 作业需求
- 天气预警
- 作业统计
---


## 环境需求
### 系统环境
- 操作系统：CentOS 7.6+/Ubuntu 20.04 LTS

### 软件依赖
- Node.js：18.20.7
- Npm：10.9.2
- Vue：3.4.31

### 硬件要求
- 最低配置：4核CPU / 8GB内存 / 100GB存储
- 屏幕分辨率：1920x1080+

---

## 安装指南
### 快速部署
```bash
# 克隆仓库
git clone http://gitee.siiri.com/enterprise/ljy-manage-web.git

# 安装依赖
npm config set registry https://registry.npmmirror.com/
npm install

# 环境配置
#

# 启动开发服务器
npm run dev

# 生产构建
npm run build
```

------

## 目录结构说明

```
frontend/  
├── public/               # 静态资源  
├── src/
│   ├── api/              # 接口请求
│   ├── assets/           # 图片/字体等资源
│   ├── components/       # 公共组件
│   ├── styles/           # 全局样式
│   ├── types/            # 全局类型接口
│   ├── router/           # 路由配置
│   ├── store/            # 状态管理
│   ├── utils/            # 公共函数
│   ├── views/            # 路由页面
│   ├── aaa.vue/          # 本地基础组件工具查看页面
│   ├── App.vue           # 页面入口
│   └── config.ts         # 全局公共属性配置
│   └── main.ts           # 主入口
├── .gitignore            # 文件忽略
├── package.json          # 依赖版本
└── vue.config.js         # 构建配置
└── .env.development      # 开发环境打包配置
└── .env.production       # 生产环境打包配置
```