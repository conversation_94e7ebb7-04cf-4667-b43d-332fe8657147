import path from 'path';
import { defineConfig, loadEnv, UserConfig } from 'vite';
import type { ProxyOptions } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import legacy from '@vitejs/plugin-legacy'; // 兼容旧版本浏览器
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
const pathSrc = path.resolve(__dirname, 'src');

const proxyMaps = {
	'/test_api': 'http://172.16.40.21/lcdd', // 测试
	'/file_api': 'http://172.16.40.38:8876/basic-service', // 上传文件专用
	'/jt_api': 'http://172.16.50.211:18080', // 景涛本地
	'/idaas_api': 'http://172.16.30.81/idaas-service', // idaas-service
};
// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
	// 根据当前工作目录中的 `mode` 加载 .env 文件
	// 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀。
	const env = loadEnv(mode, process.cwd(), '');
	const config: UserConfig = {
		base: env.VITE_baseUrl || '/',
		build: {
			outDir: env.VITE_outDir,
			assetsDir: 'assets',
			target: 'es2015',
			// minify: 'terser', // 去掉 console
			// terserOptions: {
			// 	compress: {
			// 		drop_console: true,
			// 		drop_debugger: true,
			// 	},
			// },
			
		},
		resolve: {
			alias: {
				'@/': `${pathSrc}/`,
			},
		},
		server: {
			port: 8301,
			proxy: Object.keys(proxyMaps).reduce((obj, key) => {
				obj[key] = {
					target: proxyMaps[key],
					changeOrigin: true,
					ws: true,
					rewrite: (path) => path.replace(key, ''),
				};
				return obj;
			}, {} as Record<string, string | ProxyOptions>),
		},
		css: {
			preprocessorOptions: {
				scss: {
					// additionalData: `@use "@/assets/styles/index.scss" as *;`,
				},
			},
		},
		plugins: [
			vue(),
			vueJsx(),
			AutoImport({
				resolvers: [
					// 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox... (带样式)
					ElementPlusResolver(),
				],
				dts: path.resolve(pathSrc, 'types', 'auto-imports.d.ts'), // 自动导入组件类型声明文件位置，默认根目录
			}),
			Components({
				resolvers: [
					// 自动导入 Element Plus 组件
					ElementPlusResolver(), // 自动注册图标组件
				],
				dts: path.resolve(pathSrc, 'types', 'components.d.ts'), //  自动导入组件类型声明文件位置，默认根目录
			}),
			// 浏览器兼容问题配置
			// legacy({
			// 	targets: ['defaults', 'not IE 11'],
			// 	additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
			// 	renderLegacyChunks: true,
			// 	polyfills: [
			// 		'es.symbol',
			// 		'es.promise',
			// 		'es.promise.finally',
			// 		'es/map',
			// 		'es/set',
			// 		'es.array.filter',
			// 		'es.array.for-each',
			// 		'es.array.flat-map',
			// 		'es.object.define-properties',
			// 		'es.object.define-property',
			// 		'es.object.get-own-property-descriptor',
			// 		'es.object.get-own-property-descriptors',
			// 		'es.object.keys',
			// 		'es.object.to-string',
			// 		'web.dom-collections.for-each',
			// 		'esnext.global-this',
			// 		'esnext.string.match-all',
			// 	],
			// }),
		],
		define: {
			// enable hydration mismatch details in production build
			__VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'true',
		},
	};
	return config;
});
