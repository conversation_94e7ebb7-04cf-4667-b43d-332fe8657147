<template>
	<el-dialog
		v-model="dialogVisible"
		width="600px"
		:title="title"
		:close-on-click-modal="false"
		append-to-body
		align-center
		destroy-on-close
		@closed="closeDialog"
		@open="handleOpen"
	>
		<el-form
			ref="formRef"
			style="margin: 0 12px"
			:model="formData"
			:disabled="type == 'view'"
			label-width="180px"
			:rules="formRules"
		>
			<el-row :gutter="24">
				<el-col :span="22">
					<el-form-item label="时段" prop="type">
						<el-select v-model="formData.type" placeholder="请选择">
							<el-option :key="0" label="七天" :value="0" />
							<el-option :key="1" label="一个月" :value="1" />
							<el-option :key="2" label="本年" :value="2" />
							<el-option :key="3" label="全量数据" :value="3" />
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="22">
					<el-form-item label="区域总面积（亩）" prop="areaArea">
						<el-input-number v-model="formData.areaArea" :min="0"></el-input-number>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="22">
					<el-form-item label="区域完成总面积（亩）" prop="finishedArea">
						<el-input-number @change="handleFinishedArea" v-model="formData.finishedArea" :min="0"></el-input-number>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="22">
					<el-form-item label="机械作业面积（亩）" prop="mechanicalWorkingArea">
						<el-input-number
							:disabled="!formData.finishedArea"
							@change="handleChangeMachine"
							v-model="formData.mechanicalWorkingArea"
							:min="0"
						></el-input-number>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="22">
					<el-form-item label="人工作业面积（亩）" prop="manualWorkArea">
						<el-input-number
							:disabled="!formData.finishedArea"
							@change="handleChangePerson"
							v-model="formData.manualWorkArea"
							:min="0"
						></el-input-number>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<el-row justify="center">
			<el-button @click="closeDialog" style="margin-right: 12px">取消</el-button>
			<el-button type="primary" @click="submit" v-if="type !== 'view'" style="margin-right: 12px">确认</el-button>
		</el-row>
	</el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ElForm, FormRules } from 'element-plus';
import { RequestSave, JobStatisticsBaseVO } from '../api/types';
import { api_editCenter, api_saveCenter } from '../api';
import { setObjValue } from '@/utils';
interface Props {
	type: string; //新增还是查看还是编辑
	rowData?: JobStatisticsBaseVO | null;
}
const props = withDefaults(defineProps<Props>(), {
	type: 'add',
});
const dialogVisible = defineModel<boolean>('visible');

const title = computed(() => {
	switch (props.type) {
		case 'add':
			return '新增';
		case 'view':
			return '查看';
		case 'edit':
			return '编辑';
		default:
			break;
	}
});
const emits = defineEmits<{
	(ev: 'saveForm'): void;
}>();

onMounted(() => {});
const defaultData: RequestSave = {
	areaArea: NaN, //区域总面积
	finishedArea: NaN, //区域完成总面积
	manualWorkArea: NaN, //人工完成面积
	mechanicalWorkingArea: NaN, //机械完成面积
	type: 0, //0七天 1：一个月 2:本年  3:全量数据
};

const formData = ref<RequestSave>(Object.assign({}, defaultData, { subsidyRule: [] }));
const rules: FormRules<RequestSave> = Object.keys(formData.value).reduce((acc, key) => {
	acc[key] = [
		{
			required: true,
			message: '不能为空',
			trigger: 'blur',
		},
	];
	return acc;
}, {} as FormRules);

const formRules = ref<FormRules<RequestSave>>(rules);

const formRef = ref<InstanceType<typeof ElForm>>();

function handleOpen() {
	setObjValue(formData.value, {}, defaultData);
	if (!dialogVisible.value || props.type == 'add') return;
	setObjValue(formData.value, props.rowData!, defaultData);
}
function submit() {
	formRef.value?.validate((valid) => {
		if (!valid) return;
		let param = {
			areaArea: formData.value.areaArea, //区域总面积
			finishedArea: formData.value.finishedArea, //区域完成总面积
			manualWorkArea: formData.value.manualWorkArea, //人工完成面积
			mechanicalWorkingArea: formData.value.mechanicalWorkingArea, //机械完成面积
			type: formData.value.type, //0七天 1：一个月 2:本年  3:全量数据
		};
		if (formData.value.manualWorkArea! + formData.value.mechanicalWorkingArea! != formData.value.finishedArea) {
			ElMessage.warning('请确保面积值输入正确！');
			return;
		}
		let api_ = props.type == 'add' ? api_saveCenter : api_editCenter;
		if (props.type == 'edit') param['uuid'] = props.rowData!.uuid;
		api_(param).then((res) => {
			emits('saveForm');
			dialogVisible.value = false;
		});
	});
}

function closeDialog() {
	setObjValue(formData.value, {}, defaultData);
	dialogVisible.value = false;
	formRef.value!.clearValidate();
}

defineExpose({
	open,
	close,
});

function handleFinishedArea(value) {
	formData.value.manualWorkArea = NaN;
	formData.value.mechanicalWorkingArea = NaN;
}

function handleChangeMachine(value) {
	formData.value.manualWorkArea = Number(formData.value.finishedArea!) - Number(value);
}
function handleChangePerson(value) {
	formData.value.mechanicalWorkingArea = Number(formData.value.finishedArea!) - Number(value);
}
</script>
<style lang="scss" scoped>
.add-btn-t {
	width: 100%;
	padding: 12px 0;
	display: flex;
	justify-content: center;
	border-radius: 12px;
	background-color: #ecf5ff;
	margin-bottom: 12px;
	// border: 1px solid #f2f3f5;

	cursor: pointer;
	// color: #ecf5ff;
	// border: 1px solid #ecf5ff;
}
</style>
