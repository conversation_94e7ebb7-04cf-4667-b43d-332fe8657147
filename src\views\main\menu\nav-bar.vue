<template>
	<div class="t-bar-box" ref="refDomBox">
		<div class="t-scroll" ref="refDomScroll">
			<div
				v-for="item in storeActiveRoutes.getRoutes"
				class="t-item"
				:class="{
					't-active': item.name === currentRoute.name,
				}"
				@click="onClick(item, $event)"
				@contextmenu.prevent.native="openMenu(item, $event)"
			>
				{{ item.meta!.title }}
				<el-icon title="关闭" v-show="storeActiveRoutes.getRoutes.length > 1" class="t-close" @click.stop="onClose(item)">
					<Close />
				</el-icon>
			</div>
		</div>
		<el-popover placement="bottom-end" v-if="moreList.length" trigger="hover">
			<template #reference>
				<el-icon class="t-icon-more"><MoreFilled /></el-icon>
			</template>
			<div class="t-ul">
				<div
					v-for="item in moreList"
					class="t-li g-ellipsis"
					:class="{
						't-active': item.name === currentRoute.name,
					}"
					:title="(item.meta!.title as string)"
					@click="onClick(item, $event)"
				>
					{{ item.meta!.title }}
					<el-icon title="关闭" v-show="storeActiveRoutes.getRoutes.length > 1" class="t-close" @click.stop="onClose(item)">
						<Close />
					</el-icon>
				</div>
			</div>
		</el-popover>
		<div class="t-menu-ul" ref="refDomMenu" v-show="menuShow">
			<div class="t-li g-ellipsis" @click="closeOther">关闭其它</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { onUnmounted, onMounted, watch, ref, nextTick } from 'vue';
import { MoreFilled } from '@element-plus/icons-vue';
import { RouteRecordRaw, useRoute } from 'vue-router';
import { useStoreActiveRoutes } from '@/store/modules/active-routes';
import { innerW, setStoreCommon, storeCommon } from '@/store';
import { routerInstance } from '@/router';
import { Close } from '@element-plus/icons-vue';

const storeActiveRoutes = useStoreActiveRoutes();

const refDomBox = ref<HTMLDivElement>();
const refDomScroll = ref<HTMLDivElement>();
const refDomMenu = ref<HTMLDivElement>();

const moreList = ref<RouteRecordRaw[]>([]);

const menuShow = ref(false);
let nowClickItem: RouteRecordRaw; // 当前右键点击的item

const currentRoute = useRoute(); // 当前路由
onMounted(() => {
	watch(
		() => currentRoute.name,
		(val) => {
			const routers = routerInstance.getRoutes(); //
			const obj = routers.find((item) => item.name === val);
			storeActiveRoutes.setRoute(obj!);
		},
		{
			immediate: true,
		},
	);
	document.body.appendChild(refDomMenu.value!);
	document.addEventListener('click', hideMenuList);
});
// 菜单数量或者屏幕宽度发生变化
watch(
	[() => storeActiveRoutes.getRoutes.length, innerW],
	([val]) => {
		if (!val) return;
		nextTick(() => {
			const h1 = refDomBox.value!.offsetHeight;
			const h2 = refDomScroll.value!.offsetHeight;
			let firstCount = 0; // 首个出现在第二行中的菜单
			if (h2 > h1 + 5) {
				// 5的阈值 超出了
				const children = refDomScroll.value!.children || [];
				for (let i = 0; i < children.length; i++) {
					const element = children[i] as HTMLDivElement;
					if (element.offsetTop > 10) {
						firstCount = i;
						break;
					}
				}
			}
			if (firstCount) {
				moreList.value = [...storeActiveRoutes.getRoutes].splice(firstCount);
			} else {
				moreList.value = [];
			}
		});
	},
	{
		immediate: true,
	},
);
watch(
	() => storeCommon.isLogined,
	(val) => {
		if (!val) {
			storeActiveRoutes.clear();
		}
	},
);
onUnmounted(() => {
	storeActiveRoutes.clear();
	document.removeEventListener('click', hideMenuList);
});
function hideMenuList() {
	// console.log('111111111111111111111 hideMenuList');
	menuShow.value = false;
}
function onClick(item: RouteRecordRaw, ev) {
	if (currentRoute.name === item.name) {
		// 路由重复，调用刷新路由方法
		setStoreCommon({
			refresh: storeCommon.refresh + 1,
		});
		return;
	}
	routerInstance.push(item);
}
function openMenu(item: RouteRecordRaw, ev: MouseEvent) {
	if (storeActiveRoutes.getRoutes.length <= 1) return;
	nowClickItem = item;
	menuShow.value = true;
	nextTick(() => {
		console.log('111111111111111111111', item, ev);
		refDomMenu.value!.style.top = ev.clientY + 10 + 'px';
		refDomMenu.value!.style.left = ev.clientX - refDomMenu.value!.offsetWidth + 6 + 'px';
	});
}
function onClose(item: RouteRecordRaw) {
	menuShow.value = false;
	storeActiveRoutes.removeRoute(item);
	if (currentRoute.name === item.name) {
		routerInstance.push(storeActiveRoutes.getRoutes[0]);
	}
}
function closeOther() {
	storeActiveRoutes.removeOther(nowClickItem!.name as string);
	if (currentRoute.name !== nowClickItem?.name) {
		// 如果点击的不是当前路由，跳转到那个路由
		routerInstance.push(nowClickItem);
	}
	menuShow.value = false;
}
</script>
<style lang="scss" scoped>
.t-bar-box {
	color: #4e5969;
	flex-shrink: 0;
	overflow: hidden;
	height: 44px; // 测出来的高度
	padding-right: 30px;
	position: relative;
	background-color: #fff;
	.t-icon-more {
		position: absolute;
		top: 11px;
		right: 4px;
		transform: rotate(90deg);
	}

	.t-scroll {
		display: flex;
		flex-wrap: wrap;
		position: relative;
	}
	.t-item {
		margin: 10px 6px 20px 0;
		padding: 0 24px 0 8px;
		border: 1px solid #f2f3f5;
		line-height: 26px;
		box-sizing: border-box;
		position: relative;
		cursor: pointer;
		font-size: 14px;
		white-space: nowrap;
		&.t-active {
			background-color: #f2f3f5;
			color: #1d2129;
			border-color: #f2f3f5;
		}
		&:first-of-type {
			margin-left: 8px;
		}
	}
}
.t-close {
	position: absolute;
	right: 4px;
	top: 0;
	bottom: 0;
	margin: auto;
	z-index: 2;
	&:hover {
		color: var(--el-color-error);
	}
}

.t-menu-ul {
	position: absolute;
	background-color: #fff;
	box-shadow: var(--el-box-shadow-light);
	padding: 4px;
	z-index: 3;
	font-size: 14px;
}
.t-menu-ul,
.t-ul {
	.t-li {
		padding: 4px 24px 4px 8px;
		text-align: center;
		cursor: pointer;
		position: relative;
		border-bottom: 1px solid #f2f3f5;
		&:last-child {
			border-bottom: none;
		}
	}
}
</style>
