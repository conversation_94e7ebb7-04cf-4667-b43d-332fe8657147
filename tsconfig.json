{"compilerOptions": {"baseUrl": ".", "types": ["element-plus/global"], "target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "noImplicitAny": false, "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "paths": {"@/*": ["src/*"]}, "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/store/index.ts"]}