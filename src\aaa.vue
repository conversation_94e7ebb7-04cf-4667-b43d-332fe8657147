<template>
	<div>本组组件仅作为示例，没有实际作用 新建vue文件时，可以复制这个文件的内容</div>
	<!-- <img src="@/assets/logo.png" alt="" /> -->
	<ElButton>sdfsdfsd</ElButton>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { ElButton, ElMessage } from 'element-plus';
import { debounce, cloneDeep } from 'lodash-es';

// import {} from '@element-plus/icons-vue';
// import imgUrl from './img.png'
defineOptions({ inheritAttrs: false });
// export type ModalFormInstance = InstanceType<typeof ModalForm>;
// const refElButton = ref<InstanceType<typeof ElButton>>();
interface Props {
	accept?: string;
	multiple?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
	accept: '111',
});
const emit = defineEmits<{
	(ev: 'update:value', value: string): void;
	(ev: 'change', seledObj: any): void;
}>();
// extends

type Mutable<T> = {
	-readonly [P in keyof T]: T[P];
};

// keyof  可以用来取得一个对象接口的所有 key 值

// Partial 的作用就是将传入的属性变为可选。
type Partial1 = Partial<any>;

// Required 的作用是将传入的属性变为必选项,
type Required1 = Required<any>;

// Readonly 将传入的属性变为只读选项, 源码如下
type Readonly1 = Readonly<any>;

// Pick 主要是从一个已知的类型中，取出子集，作为一个新的类型返回。
type Pick1 = Pick<Props, 'accept'>;

// Omit 以一个类型为基础支持剔除某些属性，然后返回一个新类型。
type Omit1 = Omit<Props, 'accept'>;

type Record1 = Record<string, any>;
const myObj = {
	a: 1,
	b: 2,
};
// 通过 对象反推出类型
type MyObj = typeof myObj;
// 获取类型的 keys
type MyObjKeys = keyof typeof myObj;

// in 遍历字面量类型
type Value2 = {
	[key in MyObjKeys]: MyObj[key];
};

interface InvariableParam {
	locked: boolean; // 也可以有部分固定的属性
}
// 动态键值对类型，配合泛型可以定义一个通用接口
type KeyValue<OtherParam = unknown> = InvariableParam & {
	[P in keyof OtherParam]: OtherParam[P];
};
</script>
<style lang="scss" scoped>
.t-div {
	// 引用背景图片
	// background-image: url('@/assets/logo.png');
}
</style>
