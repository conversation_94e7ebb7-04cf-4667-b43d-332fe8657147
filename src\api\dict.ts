// 获取数据字典 可以同时调用多次，会将获取后的数据缓存起来，下载再获取相同的数据字典，不再发送请求，直接从缓存中取值

import http, { EnumAim } from '@/utils/axios';
declare global {
	interface DicDataGroup {
		code: string; //
		name: string;
	}
	interface DicDataItem {
		code: string; // 		* 编码
		description: string; // 		* 描述
		groupCode: string; // 		* 分类组编码
		id: string; // 		* id
		isValid: number; // 		* 是否启用（0/1）
		level: number; // 		* 层级
		name: string; // 		* 名称
		parentCode: string; // 		* 父级编号
		sort: number; // 		* 排序
	}
	interface DicTreeItem {
		children: DicTreeItem[];
		code: string; // 		* 编码
		id: string; // 		* 数据字典唯一编号
		level: number; // 		* 层级
		name: string; // 		* 名称
		parentId: string; // 		* 父级编号
	}
	interface DicDataItemCode {
		code: string;
		extend: string;
		fullPath: string;
		level: number;
		name: string;
		parentCode: string;
		parentUuid: string;
		sort: number;
		updateNumber: string;
		uuid: string;
	}
	interface AreaBase {
		areaId: string;
		areaName: string;
		level: number;
		parentAreaId: string;
	}
	interface AreaTreeBase extends AreaBase {
		children: AreaTreeBase[];
	}
}

enum ApiCommon {
	getArea = '/web-api/area/v1/list', //获取地市字典
	getDict = '/web-api/dict/v1/list/{code}', //  数据字典
	getDicts = '/web-api/manager/v1/dict/mapList/{groupNumbers}', //  数据字典 数组
	getTreeDict = '/web-api/manager/v1/dict/listTree/{groupNumber}', //  数据字典  树结构
	getTreeDicts = '/web-api/manager/v1/dict/mapListTree/{groupNumbers}', //  数据字典   树结构 数组
}
const aim = EnumAim.test;

// 储存数据字典数据的 map key是数据字典类型，value 是值 值可能为null 即处在等待的状态
const map_dicData = new Map<string, DicDataItem[] | null>();

const set_finishKeys = new Set<string>(); // 加载完成的字典的key集合
// 储存请求的 promise  key是请求参数，value是promise对象的 resolve 回调方法
const map_resolve = new Map<string[], Function>();
// 储存请求的 promise  key是请求参数，value是promise对象的 reject 回调方法
const map_reject = new Map<string[], Function>();

type DataMap = Record<string, DicDataItem[]>;

// 用来记录当前发送中的请求的数量
let sendingCount = 0;

// 'BuildNature',//建筑性质,   'SurveyLevel',// 勘察等级 ,   'EngineeringType',//工程类型 ,   'constructScope',//建筑规模
// 'EngineeringPurpose',//工程用途   'ProjectLevel',//工程等级    'StructuralSystem',//结构体系
// 'GroundFoundationLevel',//地基基础设计等级   'FoundationType',//基础形式    'GroundSoilType',//场地土类别
// 'BuildSiteType',//建筑场地类别   'GroundProcess',//地基处理方法  'BaseHoleType',//基坑类型
// 'AntiSeismicIntensity',//抗震设防类别  'AntiSeismicType',//抗震设防烈度 'FireLevel',//防火(耐火)等级
// 'FeedWaterWay',//给水方式   'HeatingWay',//采暖方式   'VentilationWay',//空调通风方式  'LightingWay',//照明方式
// 'GreenBuildDesign',//绿色建筑设计标准
/**
 * @description: 获取数据字典 多个 可以同时调用多次，会将获取后的数据缓存起来，下载再获取相同的数据字典，不再发送请求，直接从缓存中取值
 */
export function api_getDicts(keys_: string[], isErrorTips: boolean = false): Promise<DataMap> {
	const keys = [...keys_];
	// 获取数据字典时，先从 store 里获取最新的数据字典，如果不存在，再去后台获取
	const dicMap: DataMap = {};
	const emptyKeys: string[] = []; // store中不存在的key
	const waitKeys: string[] = []; //  在等待中的keys
	keys.forEach((key) => {
		if (set_finishKeys.has(key)) {
			// 已经加载完成
			dicMap[key] = map_dicData.get(key) as DicDataItem[];
		} else if (map_dicData.has(key)) {
			// 没有完成，在等待中。。
			waitKeys.push(key);
		} else {
			// 第一次请求
			map_dicData.set(key, null);
			emptyKeys.push(key);
		}
	});

	if (!emptyKeys.length && !waitKeys.length) {
		// 如果没有 emptyKeys 和 waitKeys 说明不需要其它处理，直接返回值
		return Promise.resolve(dicMap);
	} else if (!emptyKeys.length && waitKeys.length) {
		// 仅仅存在等待的值， 返回自定义 promise 等待结果后来后再触发回调方法
		return new Promise<DataMap>((resolve, reject) => {
			map_resolve.set(keys, resolve);
			map_reject.set(keys, reject);
		});
	} else if (emptyKeys.length && !waitKeys.length) {
		// 仅仅存在 发送的值 直接返回 请求实体
		return getData(emptyKeys);
	} else if (emptyKeys.length && waitKeys.length) {
		// 两个值都存在 不返回请求实体，但是 触发获取请求的方法，并返回自定义 promise 等待结果后来后再触发回调方法
		getData(emptyKeys);
		return new Promise<DataMap>((resolve, reject) => {
			map_resolve.set(keys, resolve);
			map_reject.set(keys, reject);
		});
	}
	return Promise.reject('发生错误，请刷新重试');
	// 真实的请求方法
	function getData(emptyKeys: string[]): Promise<DataMap> {
		console.log('数据字典接口发送了真实的请求 emptyKeys', emptyKeys);
		sendingCount++; //发送中的请求数量+1
		const url = ApiCommon.getDicts.replace('{groupNumbers}', emptyKeys + '');
		return http
			.get(url, null, {
				aim,
				errorTips: isErrorTips,
				holdRepeat: false, // 可以重复请求，切换路由时不取消
			})
			.then(
				(res) => {
					const mapData: DataMap = res[0] || {};
					sendingCount--; // 成功后，-1
					//请求返回后，可能传入的key没有数据字典，返回结果中就没有该key，此时将该key的数据字典置为空
					emptyKeys.forEach((key) => {
						const arrs = mapData[key] || [];
						// const dicItme = arrs.filter((item) => item.dataDictionaryNumberInt !== -10);
						// dicItme.sort((a, b) => a.sortOrder - b.sortOrder);
						map_dicData.set(key, arrs);
						set_finishKeys.add(key);
						dicMap[key] = arrs;
					});
					console.log('数据字典接口发送的真实的请求 回来了 map_resolve', map_resolve);
					emit(); // 每次真实的请求回来时，都遍历一遍 map_resolve 触发里边的 promise
					return dicMap;
				},
				(err) => {
					sendingCount--; // 失败后，也 -1
					emit(); // 每次真实的请求回来时，都遍历一遍 map_resolve 触发里边的 promise
					return Promise.reject(err);
				},
			);
	}
	// 触发 Promise
	function emit() {
		// 当最后一个请求回来时 触发所有的事件
		if (sendingCount <= 0) {
			map_resolve.forEach(function (value, keys_) {
				const dicMap_: Record<string, DicDataItem[] | null | undefined> = {};
				// 数组中的每一个key都回来了
				const has = keys_.every((key) => {
					dicMap_[key] = map_dicData.get(key);
					return set_finishKeys.has(key);
				});
				// 如果所有的字典都回来了
				if (has) {
					value(dicMap_);
				} else {
					// 说明有的字典失败了
					const func = map_reject.get(keys_);
					func && func(dicMap_);
				}
				map_resolve.delete(keys_);
				map_reject.delete(keys_);
			});
			// 处理完
		}
	}
}
export function api_getDict(groupCode: string): Promise<DicDataItemCode[]> {
	// if (isDev) return resolveFunc();
	const url = ApiCommon.getDict.replace('{code}', groupCode);
	return http
		.get(
			url,
			{
				// limit: 0,
				// offset: 0,
			},
			{
				aim,
				cacheResult: true,
			},
		)
		.then((res) => res || []);
}

export function api_getTreeDict(groupNumber: string): Promise<DicTreeItem[]> {
	// if (isDev) return resolveFunc();
	const url = ApiCommon.getTreeDict.replace('{groupNumber}', groupNumber);
	return http.get(url, null, {
		aim,
		cacheResult: true,
	});
}
export function api_getTreeDicts(groupNumbers: string): Promise<DicTreeItem[]> {
	const url = ApiCommon.getTreeDicts.replace('{groupNumbers}', groupNumbers + '');
	return http.get(url, null, {
		aim,
		cacheResult: true,
	});
}
export function api_getArea(): Promise<AreaTreeBase[]> {
	// if (isDev) return resolveFunc();
	const url = ApiCommon.getArea;
	return http
		.get(
			url,
			{
				// limit: 0,
				// offset: 0,
			},
			{
				aim,
				cacheResult: true,
			},
		)
		.then((res) => buildTree(res) || []);
}
function buildTree(areas: AreaBase[]) {
	// 1. 创建映射表：areaId -> 节点
	const map = {};
	areas.forEach((area) => {
		map[area.areaId] = { ...area, children: [] };
	});

	// 2. 构建树形结构
	const tree: AreaTreeBase[] = [];
	areas.forEach((area) => {
		const node = map[area.areaId];
		const parentId = area.parentAreaId;

		if (parentId === '' || !map[parentId]) {
			// 根节点（parentId 为空或父节点不存在）
			tree.push(node as AreaTreeBase);
		} else {
			// 子节点：添加到父节点的 children 中
			map[parentId].children.push(node);
		}
	});

	return tree[0].children || [];//去除根级中国
}
