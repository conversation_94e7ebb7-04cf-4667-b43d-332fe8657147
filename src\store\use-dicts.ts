import { reactive, ToRefs, toRefs } from 'vue';
import { api_getDicts } from '@/api/dict';

function useDicts(arr: string[], noToRefs: true): Record<string, DicDataItem[]>;
function useDicts(arr: string[]): ToRefs<Record<string, DicDataItem[]>>;

// 'BuildNature',//建筑性质,   'SurveyLevel',// 勘察等级 ,   'EngineeringType',//工程类型 ,   'constructScope',//建筑规模
// 'EngineeringPurpose',//工程用途   'ProjectLevel',//工程等级    'StructuralSystem',//结构体系
// 'GroundFoundationLevel',//地基基础设计等级   'FoundationType',//基础形式    'GroundSoilType',//场地土类别
// 'BuildSiteType',//建筑场地类别   'GroundProcess',//地基处理方法  'BaseHoleType',//基坑类型
// 'AntiSeismicIntensity',//抗震设防类别  'AntiSeismicType',//抗震设防烈度   'FireLevel',//防火(耐火)等级
// 'FeedWaterWay',//给水方式   'HeatingWay',//采暖方式   'VentilationWay',//空调通风方式  'LightingWay',//照明方式
// 'GreenBuildDesign',//绿色建筑设计标准  'approvallevel',//立项等级    'UserProjectRole',//查询企业下人员
function useDicts(arr: string[], noToRefs?: true): ToRefs<Record<string, DicDataItem[]>> | Record<string, DicDataItem[]> {
	const arrObj = arr.reduce(
		(obj, prev) => {
			obj[prev] = [];
			return obj;
		},
		{
			// 数据是否准备好 可以监听这个属性来判断数据字典加载完成
			beReady: false as any,
		},
	);
	const dicts = reactive<Record<string, DicDataItem[]>>(arrObj);
	api_getDicts(arr).then((maps) => {
		arr.forEach((key) => {
			dicts[key] = maps[key] || [];
		});
		dicts.beReady = true as any;
	});
	return noToRefs ? dicts : toRefs(dicts);
}
export default useDicts;
