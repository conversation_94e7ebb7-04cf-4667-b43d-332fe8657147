export interface getListRequestType {
	key?: string;
	pageNo: number;
	pageSize: number;
	[property: string]: any;
}

/**
 * PageResultCenterUserBaseVO
 */
export interface PageResultCenterUserBaseVO {
	list: CenterUserBaseVO[];
	total: number;
	[property: string]: any;
}

/**
 * CenterUserBaseVO
 */
export interface CenterUserBaseVO {
	centerId?: string; //区域化服务中心id
	centerName?: string; //服务中心名称
	cityCode?: string; //地市编码
	cityName?: string;
	districtCode?: string; //区县编码
	districtName?: string;
	provinceCode?: string; //省份编码
	provinceName?: string;
	remark?: string; //备注
	sysUserName?: string; //用户名称
	sysUserUuid?: string; //系统用户uuid
	userTelephone?: string; //用户手机号
	uuid?: string;
	[property: string]: any;
}

/**
 * SaveCenterDTO 保存服务中心传参
 */

export interface RequestSaveCenter {
	centerUuid?: string; //中心uuid
	remark?: string; //备注
	userName?: string; //    用户名
	userTelephone?: string; //    用户手机号
	[property: string]: any;
}


export interface RequestCenterList {
	cityCode?: string;
	districtCode?: string;
	provinceCode?: string;
	tenantId?: string;
	[property: string]: any;
}

export interface CenterList {
	centerName: ''; //区域总面积
	centerUuid: ''; //区域完成总面积
	[property: string]: any;
}
