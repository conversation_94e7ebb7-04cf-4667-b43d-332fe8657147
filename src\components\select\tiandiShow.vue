<template>
	<div class="map_show_box">
		<div class="title">
			地址
			<div class="del" @click="$emit('close')">
				<img src="@/assets/images/close.png" alt />
			</div>
		</div>

		<div class="content">
			<div class="input_search">
				<el-autocomplete
					v-if="action == 'edit'"
					ref="el_auto"
					class="inline-input"
					v-model="emitAdress.name"
					:fetch-suggestions="querySearchFn"
					placeholder="请输入内容"
					@select="handleSubmit"
					:trigger-on-focus="false"
					:popper-class="noData ? 'platform-auto-complete' : ''"
				></el-autocomplete>
				<div v-else>
					{{ emitAdress.name }}
				</div>
			</div>

			<div id="map" class="tdu_map"></div>
		</div>
		<ul class="lng_lat_line">
			<li>
				<span>经度：</span><span>{{ emitAdress.lng }}</span>
			</li>
			<li>
				<span>纬度：</span><span>{{ emitAdress.lat }}</span>
			</li>
		</ul>
		<div class="footer_btn">
			<el-button v-if="action == 'edit'" type="primary" :disabled="noData" @click="confirmBtn">确认</el-button>
		</div>
	</div>
</template>

<script>
import _ from 'lodash';
let T;
export default {
	props: {
		mapInitAddress: {
			type: Object,
			default: () => {
				return {
					name: '',
					lng: '',
					lat: '',
				};
			},
		},
		action: {
			type: String,
			default: 'edit',
		},
	},
	data() {
		return {
			map: '',
			submarker: '',
			clickMarker: '',
			// inputValue: "",
			geocoder: '',
			searchPoiArr: [],
			emitAdress: {
				name: '',
				lng: '',
				lat: '',
			},
			mapInitLng: '',
			mapInitLat: '',
			noData: false,
		};
	},
	created() {
		this.mapInitLng = Number(this.userInfo.lon || 106.54655446278467);
		this.mapInitLat = Number(this.userInfo.lat || 29.56581038874143);
		this.$nextTick(() => {
			this.emitAdress = Object.assign(this.mapInitAddress);
			this.init();
		});
	},
	mounted() {
		// this.inputValue = this.mapInitAddress.name;
	},
	computed: {
		userInfo() {
			return this.$store?.state.user.userInfo || {};
		},
	},
	beforeDestroy() {
		this.map.removeEventListener('click', this.clickSearchResult);
	},
	methods: {
		// 地图初始化
		init() {
			T = window.T;
			this.map = new T.Map('map');

			this.map.centerAndZoom(new T.LngLat(this.mapInitLng, this.mapInitLat), 14);
			this.map.setMinZoom(6); // 设置最小变动层级
			this.map.setMaxZoom(18); // 设置最大变动层级
			this.map.enableScrollWheelZoom(); // 启用滚轮放大缩小
			this.map.setMapType(TMAP_NORMAL_MAP); // 街道

			// 鼠标点击逆地理搜索
			this.geocoder = new T.Geocoder();
			this.map.addEventListener('click', (e) => {
				if (this.action == 'detail') return;
				this.geocoder.getLocation(e.lnglat, this.clickSearchResult);
			});
			// 根据传过来的经纬度打点
			if (this.mapInitAddress.lng && this.mapInitAddress.lat) {
				this.map.panTo({ lng: this.mapInitAddress.lng, lat: this.mapInitAddress.lat }, 14);
				// 创建标注对象
				this.submarker = new T.Marker({
					lng: this.mapInitAddress.lng,
					lat: this.mapInitAddress.lat,
				});
				// 向地图上添加标注
				this.map.addOverLay(this.submarker);
			}
		},

		localSearchResult(result) {
			// console.log(222, result.pois)
			this.searchPoiArr = [];
			this.searchPoiArr = result.pois;
		},
		// 点击地图事件
		clickSearchResult(result) {
			// var T = window.T
			this.map.removeOverLay(this.clickMarker); // 清除点位
			this.map.removeOverLay(this.submarker); // 清除点位
			if (result.getStatus() == 0) {
				this.map.panTo(result.getLocationPoint(), 14);
				// 创建标注对象
				this.clickMarker = new T.Marker(result.getLocationPoint());
				// 向地图上添加标注
				this.map.addOverLay(this.clickMarker);

				// 记录经纬度地名
				this.emitAdress.name = result.getAddress();
				this.emitAdress.lng = result.getLocationPoint().lng;
				this.emitAdress.lat = result.getLocationPoint().lat;
				console.log(this.emitAdress);
			} else {
				this.map.clearOverLays();
				// console.log(result.getMsg());
			}
		},
		// input联想搜索
		querySearchFn: _.debounce(function (queryString, cb) {
			console.log(11111, queryString);
			if (!queryString) return;

			let config = {
				pageCapacity: 20,
				onSearchComplete: (result) => {
					console.log('res', result);
					this.searchPoiArr = [];
					if (result.pois && result.pois.length > 0) {
						this.noData = false;
						// console.log(result.pois);
						this.searchPoiArr = result.pois.map((item) => {
							return { value: item.address + item.name, lonlat: item.lonlat };
						});
						// console.log(this.searchPoiArr[0]);
						// this.emitAdress.name = this.searchPoiArr[0].value;
						// this.emitAdress.lng = this.searchPoiArr[0].lonlat.split(",")[0];
						// this.emitAdress.lat = this.searchPoiArr[0].lonlat.split(",")[1];
						// console.log(this.emitAdress);
					} else {
						this.noData = true;
						this.searchPoiArr = [{ value: '无匹配数据', lonlat: 0 }];
					}
					cb(this.searchPoiArr);
				},
			};

			let localsearch = new T.LocalSearch(this.map, config);
			localsearch.search(queryString, 1);
		}, 1000), // 设置防抖延迟时间为 1000 毫秒
		// 下拉框选中
		handleSubmit(data) {
			console.log('data', data);
			this.map.removeOverLay(this.clickMarker); // 清除点位
			this.map.panTo({ lng: data.lonlat.split(',')[0], lat: data.lonlat.split(',')[1] }, 14);
			// 创建标注对象
			this.clickMarker = new T.Marker({
				lng: data.lonlat.split(',')[0],
				lat: data.lonlat.split(',')[1],
			});
			// 向地图上添加标注
			this.map.addOverLay(this.clickMarker);

			// 记录经纬度地名
			this.emitAdress.name = data.value;
			this.emitAdress.lng = data.lonlat.split(',')[0];
			this.emitAdress.lat = data.lonlat.split(',')[1];
			console.log(this.emitAdress);
		},
		// 传值给父组件
		confirmEmit() {
			// 参数配置默认的经纬度
			// console.log("参数配置默认的经纬度", this.mapInitAddress);
			this.emitAdress.name = this.emitAdress.name || this.mapInitAddress.name;
			this.emitAdress.lng = this.emitAdress.lng || this.mapInitAddress.lng;
			this.emitAdress.lat = this.emitAdress.lat || this.mapInitAddress.lat;
			this.$emit('emitAdressMethod', this.emitAdress);
		},
		confirmBtn() {
			// console.log(this.emitAdress);
			this.confirmEmit();
		},
	},
};
</script>
<style lang="scss">
.map_show_box {
	width: 1000px;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 200001; // loading index2000
	background: linear-gradient(180deg, #ceeaff 0%, #f3fdff 100%),
		linear-gradient(180deg, rgba(202, 225, 255, 0.96) 0%, rgba(255, 255, 255, 0.5) 100%);
	box-shadow: 0px 7px 18px 0px rgba(0, 0, 0, 0.15);
	border-radius: 6px;
	padding: 10px 20px 0px 20px;
	.title {
		margin-bottom: 10px;
		line-height: 46px;
		font-size: 18px;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: rgba(0, 0, 0, 0.85);
		position: relative;
		height: 46px;
		background: url('@/assets/images/top_title_bg.png') no-repeat 82% 100%;
		background-size: 291px 44px;
		.del {
			img {
				cursor: pointer;
				position: absolute;
				right: 0px;
				top: 15px;
				width: 20px;
				height: 20px;
			}
		}
	}
	.content {
		background: #fff;
		.input_search {
			padding: 10px 0px 0 10px;
			display: flex;
			margin-bottom: 10px;
			line-height: 30px;
			.inline-input {
				width: 500px;
			}
		}
		.tdu_map {
			width: 100%;
			height: 360px;
			z-index: 0;
		}
	}
	.lng_lat_line {
		margin: 10px 0 0 10px;
		display: flex;
		li {
			list-style-type: none;
			display: flex;
			font-size: 16px;
			&:last-of-type {
				margin-left: 30px;
			}
		}
	}
	.footer_btn {
		height: 61px;
		line-height: 61px;
		bottom: 0;
		left: 0;
		width: 100%;
		background: url('@/assets/images/bottom_title_bg.png') no-repeat 2.7% 100%;
		background-size: 555px 44px;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		.sub_btn {
			background: var(--theme) !important;
			&.noClick {
				cursor: not-allowed;
			}
		}
	}
}
.el-popper {
	z-index: 2000012 !important;
}
// input提示暂无的时候，禁止点击
.platform-auto-complete {
	z-index: 2000012 !important;
	.el-autocomplete-suggestion__wrap {
		padding: 5px 0;
		ul {
			cursor: not-allowed;
			li {
				pointer-events: none; // 阻止可点击事件
				.default {
					text-align: center;
					color: #999;
				}
				&:hover {
					background-color: #fff;
				}
			}
		}
	}
}
</style>
