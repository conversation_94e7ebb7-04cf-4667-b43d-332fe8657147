<template>
	<container-lcr ref="refContainer" :title="false" :topHeight="64">
		<template #top>
			<div style="width: 75%; height: 32px" class="form-b">
				<el-form class="g-form-box" ref="refForm" :model="formState" label-position="left">
					<el-form-item class="g-col-33" label="服务类型" prop="jobType">
						<el-select v-model="formState.jobType" placeholder="请选择">
							<el-option v-for="item in center_service_type" :key="item.code" :label="item.name" :value="item.code" />
						</el-select>
					</el-form-item>
					<el-form-item class="g-col-33" label="行政区划" prop="userTelephone">
						<el-cascader
							placeholder="请选择行政区划"
							:props="{
								value: 'areaId',
								label: 'areaName',
								children: 'children',
							}"
							style="width: 100%"
							v-model="formState.userTelephone"
							:options="areas"
							@change="handleChange"
						/>
					</el-form-item>
					<el-form-item class="g-col-33" label="状态" prop="jobStatus">
						<el-select v-model="formState.jobStatus" placeholder="请选择状态">
							<el-option :key="0" label="草稿" :value="0" />
							<el-option :key="1" label="待响应" :value="1" />
							<el-option :key="2" label="进行中" :value="2" />
							<el-option :key="3" label="已完成" :value="3" />
						</el-select>
					</el-form-item>
				</el-form>
			</div>
			<el-button type="primary" @click="clickSearchBtn">查询</el-button>
			<el-button type="" @click="clickResetBtn">重置</el-button>
		</template>
		<div class="table-box-wrap">
			<common-table
				:otherHeight="416"
				ref="refTable"
				:columns="columnsCommon"
				:getList="getList"
				:total="pageTotal"
				:data="tableData"
				:operateWidth="250"
				:get-btns="btns"
				:show-check-box="true"
				@selection-change="onChangeSelection"
			>
				<template #header>
					<div class="btn-box">
						<el-button type="primary" @click="clickAddCenterBtn" :icon="Plus">新增</el-button>
						<i-upload
							style="margin: 0 12px"
							:multiple="false"
							:accept="['xls', 'xlsx']"
							:fileSize="50"
							:beforeUpload="uploadBefore"
						>
							<el-button @click="" :icon="Download">批量导入</el-button>
						</i-upload>
						<el-button @click="getImportTemeplete" :icon="Download">导入模板</el-button>
						<el-popconfirm title="是否确定要删除所选数据？" @confirm="clickDeleteMore">
							<template #reference>
								<el-button :disabled="multipleSelection.length === 0" :icon="Delete">批量删除</el-button>
							</template>
						</el-popconfirm>
						<el-button @click="clickExportTable" :icon="Upload">导出</el-button>
					</div>
				</template>
			</common-table>
		</div>
		<AddCenterDialog
			:rowData="activeRow"
			:type="dialogType"
			v-model:visible="openBaseDialog"
			@saveForm="refreshTableData"
		></AddCenterDialog>
	</container-lcr>
</template>

<script lang="ts" setup>
import { cloneDeep } from 'lodash-es';
import { onMounted, ref } from 'vue';
import { Delete, Upload, Plus, Download } from '@element-plus/icons-vue';
import { api_delete, api_finish, api_getList, api_import, api_refuse } from './api/index';
import { JobBaseVO } from './api/types';
import AddCenterDialog from './components/addCenterDialog.vue';
import { XMLHttpDownload2 } from '@/utils/axios';
import { commonConfirm, exportToExcelWithCustomHeaders, getRegionFullNameTS } from '@/utils';
import { api_getArea, api_getDict } from '@/api/dict';
import { defaultText } from '@/config';
const columnsCommon: CommonTableColumn<JobBaseVO>[] = [
	{
		label: '行政区划',
		prop: 'cityCode',
		formatter(row, column, cellValue, index) {
			return getRegionFullNameTS(areas.value, row.provinceCode, row.cityCode, row.districtCode);
		},
	},
	{
		label: '服务类型',
		prop: 'jobType',
		formatter(row, column, cellValue, index) {
			let str = '';
			let typeValue = cellValue.split(',');
			typeValue.forEach((r: string, index: number) => {
				center_service_type.value.forEach((d) => {
					if (d.code == r) str += d.name;
					if (index != typeValue.length - 1 && d.code == r) str += '、';
				});
			});
			return str;
		},
	},
	{
		label: '需求详情',
		prop: 'jobInfo',
	},
	{
		label: '联系人',
		prop: 'contactName',
	},
	{
		label: '联系电话',
		prop: 'contactTelephone',
	},
	{
		label: '状态',
		prop: 'jobStatus',
		formatter(row, column, cellValue, index) {
			switch (cellValue) {
				case 0:
					return '草稿';
				case 1:
					return '待响应';
				case 2:
					return '进行中';
				case 3:
					return '已完成';
				default:
					return defaultText;
			}
		},
	},
];
const tableData = ref<JobBaseVO[]>([]);

const pageTotal = ref(0);
const activeRow = ref<JobBaseVO | null>(null);
const btns: OptionBtn<JobBaseVO>[] = [
	{
		label: '详情',
		onClick(row) {
			dialogType.value = 'view';
			activeRow.value = row;
			openBaseDialog.value = true;
		},
	},
	{
		label: '编辑',
		onClick(row) {
			dialogType.value = 'edit';
			activeRow.value = row;
			openBaseDialog.value = true;
		},
	},
	{
		label: '确认完成',
		hide: (row) => {
			return row.jobStatus != 2;
		},
		tips: '请确认作业已全部完成，可以结束本次需求！',
		onClick(row) {
			activeRow.value = row;
			api_finish(row.uuid).then((res) => {});
		},
	},
	{
		label: '取消进行',
		hide: (row) => {
			return row.jobStatus != 2;
		},
		tips: '确认拒绝当前响应者？拒绝后可以重新获得其他用户的响应',
		onClick(row) {
			activeRow.value = row;
			api_refuse(row.uuid).then((res) => {});
		},
	},
];

const refTable = ref<CommonTableInstance>();
function getList(param?: CommonTableParams) {
	let data = {
		pageNo: param?.pageNumber || 1,
		pageSize: param?.pageSize || 20,
		jobType: formState.value.jobType, // 	* 名称
		jobStatus: formState.value.jobStatus,
		// userTelephone: formState.value.centerName,
	};
	return api_getList(data).then((info) => {
		tableData.value = info.list;
		pageTotal.value = info.total;
	});
}

const center_service_type = ref<DicDataItemCode[]>([]);
const areas = ref<AreaTreeBase[]>([]);
onMounted(() => {
	api_getDict('center_service_type').then((res) => {
		center_service_type.value = res;
	});
	api_getArea().then((res) => {
		areas.value = res;
	});
	refTable.value?.refreshList();
});
function clickSearchBtn() {
	refTable.value?.refreshList();
}
function clickResetBtn() {
	formState.value = cloneDeep(defaultForm);
	refTable.value?.refreshList();
}
const refContainer = ref<ContainerLcrInstance>();

interface quereType {
	jobType: string;
	jobStatus: string;
	userTelephone: string;
}
const refForm = ref<ElFormInstance>();
const defaultForm: quereType = {
	jobType: '', // 	* 名称
	jobStatus: '',
	userTelephone: '',
};
const formState = ref<quereType>(cloneDeep(defaultForm));

const openBaseDialog = ref<boolean>(false); //是否打开新增弹窗
const dialogType = ref('add');
function clickAddCenterBtn() {
	dialogType.value = 'add';
	openBaseDialog.value = true;
}
function refreshTableData() {
	refTable.value?.refreshList();
}
const multipleSelection = ref<JobBaseVO[]>([]);
function onChangeSelection(rows: JobBaseVO[]) {
	multipleSelection.value = rows;
}

function clickDeleteMore() {
	let num = multipleSelection.value.length;
	multipleSelection.value?.forEach((row) => {
		api_delete(row.uuid!).then((res) => {
			num--;
			if (num === 0) {
				ElMessage.success('操作成功');
				refTable.value?.refreshList();
			}
		});
	});
}
function uploadBefore(files: File[]) {
	console.log(files);
	if (files && files.length) {
		let file = files[0];
		api_import(file).then(() => {
			refTable.value?.resetList();
		});
	}
	return Promise.reject(false);
}
function getImportTemeplete() {
	XMLHttpDownload2('/web-api/file/v1/template?fileName=job.xlsx', {
		fileName: '需求导入模板.xlsx',
	}).then((res) => {
		console.log(res);
	});
}

function clickExportTable() {
	// 使用示例
	const headers = {
		cityCode: '行政区划',
		jobType: '服务类型',
		jobInfo: '需求详情',
		contactName: '联系人',
		contactTelephone: '联系电话',
		status2: '状态',
	};
	let exportArr = multipleSelection.value.length == 0 ? tableData.value : multipleSelection.value;
 
	exportToExcelWithCustomHeaders(exportArr, headers, '需求.xlsx', {
		columnWidths: [20, 20, 40, 50, 20, 20],
	});
}

const handleChange = (value) => {
	formState.value.userTelephone = value;
};
</script>
<style lang="scss" scoped>
.query-box {
	display: flex;
	padding: 16px;
	box-sizing: border-box;
}
</style>
