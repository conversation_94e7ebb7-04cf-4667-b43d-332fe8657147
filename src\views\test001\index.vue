<template>
	<container-lcr ref="refContainer" :right-width="500" v-model:show-right="showRight">
		<ICenter />
		<template #right="{ show, data, title }">
			<IRight1 v-if="show && data.type === 'right1'" v-bind="data" :title="title || '标题'"></IRight1>
			<IRight2 v-if="show && data.type === 'right2'" v-bind="data" :title="title || '标题'"></IRight2>
		</template>
	</container-lcr>
</template>

<script lang="ts" setup>
import { ref, watch, inject } from 'vue';
import ICenter from './center.vue';
import IRight1 from './right-1.vue';
import IRight2 from './right-2.vue';
const refContainer = ref<ContainerLcrInstance>();
const showRight = ref(false);
</script>
<style lang="scss" scoped></style>
