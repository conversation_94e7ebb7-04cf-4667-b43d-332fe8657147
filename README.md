# ljy-screen

#### 介绍
{**以下是 Gitee 企业私有云说明，您可以替换此简介**
Gitee Premium 是 Gitee 企业级私有化部署方案，提供代码版本管理、项目管理、需求管理、缺陷管理、文档协作等功能，支持与企业内部 LDAP、项目管理、测试、部署、容器等平台的对接。}

#### 软件架构
软件架构说明


#### 安装教程

1.  npm切换至淘宝镜像 npm config set registry https://registry.npmmirror.com
2.  安装依赖包 npm install
3.  启动项目 npm run dev


#### 使用说明

1.  Node建议  18.*
2.  npm建议 10.*


#### 全局秘钥
1.  天地图 开发者Key：af7e155e27609fcc6549fad01e834779
2.  本地调试的enterpriseId  631046874cedfd0007df0758

#### 登录账号
1.  测试：http://************/iip-isv/open-service/center/home 15560017008/qq706624664

测试环境发布的config配置：
{
	"documentTitle": "林机云管理系统",
	"VITE_APP_NAME": "ljy-manage",
	"VITE_BASE_API": "http://************/lcdd",
	"VITE_API_MENU_LIST": "/web/portal/menu/list",
	"VITE_CAS_AUTH_URL": "http://************/iip-tenant-cas-client",
	"VITE_CAS_LOGOUT_URL": "http://************/iip-tenant-sso",
	"VITE_IDAAS_API": "http://*************:8851/idaas-service",
	"VITE_client_id": "8700ddc4-7943-4d7d-9e4c-eea997e0dc8e",
	"VITE_client_secret": "02a23f121394f626a58b9d6cee10a90b",
	"VITE_BASE_FILE_API": "http://************/basic-service",
	"VITE_BASE_FILE_SATIC_PATH": "http://************/wscp-file"
}
生产环境的config配置：
{
	"documentTitle": "林机云管理系统",
	"VITE_APP_NAME": "ljy-manage",
	"VITE_BASE_API": "https://lcjx.agrimachcloud.com/lcdd",
	"VITE_API_MENU_LIST": "/web/portal/menu/list",
	"VITE_CAS_AUTH_URL": "https://lcjx.agrimachcloud.com/iip-tenant-cas-client",
	"VITE_CAS_LOGOUT_URL": "https://lcjx.agrimachcloud.com/iip-tenant-sso",
	"VITE_IDAAS_API": "https://lcjx.agrimachcloud.com/idaas-service",
	"VITE_client_id": "05e1b625-daac-462c-ad71-398553a9a2ce",
	"VITE_client_secret": "58d3dbebf007db8b647f865bc81d90e8",
	"VITE_BASE_FILE_API": "https://lcjx.agrimachcloud.com/basic-service",
	"VITE_BASE_FILE_SATIC_PATH": "https://lcjx.agrimachcloud.com/wscp-file"
}

