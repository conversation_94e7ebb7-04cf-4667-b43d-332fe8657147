<template>
	<container-lcr ref="refContainer" :title="false" :topHeight="64">
		<template #top>
			<div style="width: 50%; height: 32px" class="form-b">
				<el-form class="g-form-box" ref="refForm" :model="formState" label-position="left">
					<el-form-item class="g-col-50" label="设备编号" prop="deviceNumber">
						<el-input v-model="formState.deviceNumber" placeholder="请输入设备编号" />
					</el-form-item>
					<el-form-item class="g-col-50" label="林机类型" prop="equipmentCode">
						<el-input v-model="formState.equipmentCode" placeholder="请输入林机类型" />
					</el-form-item>
					<!-- <el-form-item class="g-col-33" label="归属单位" prop="userTelephone">
						<el-input v-model="formState.userTelephone" placeholder="请输入归属单位" />
					</el-form-item> -->
				</el-form>
			</div>
			<el-button type="primary" @click="clickSearchBtn">查询</el-button>
			<el-button type="" @click="clickResetBtn">重置</el-button>
		</template>
		<div class="table-box-wrap">
			<common-table
				:otherHeight="416"
				ref="refTable"
				:columns="columnsCommon"
				:getList="getList"
				:total="pageTotal"
				:data="tableData"
				:show-check-box="true"
				@selection-change="onChangeSelection"
			>
				<template #slotName1="{ row, column, $index }">
					<el-switch
						v-model="row.status"
						:active-value="1"
						:inactive-value="0"
						class="ml-2"
						inline-prompt
						style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
						active-text="ON"
						inactive-text="OFF"
						@change="(e) => handleStatusChange(e, row)"
					/>
				</template>
				<template #header>
					<div class="btn-box">
						<el-button type="primary" @click="clickAddCenterBtn" :icon="Plus">新增</el-button>
						<i-upload
							style="margin: 0 12px"
							:multiple="false"
							:accept="['xls', 'xlsx']"
							:fileSize="50"
							:beforeUpload="uploadBefore"
						>
							<el-button @click="" :icon="Download">批量导入</el-button>
						</i-upload>
						<el-button @click="getImportTemeplete" :icon="Download">导入模板</el-button>
						<el-popconfirm title="是否确定要删除所选数据？" @confirm="clickDeleteMore">
							<template #reference>
								<el-button :disabled="multipleSelection.length === 0" :icon="Delete">批量删除</el-button>
							</template>
						</el-popconfirm>
						<el-button @click="clickExportTable" :icon="Upload">导出</el-button>
						<el-button @click="refreshTableData" :icon="Refresh">刷新</el-button>
						<el-button :disabled="multipleSelection.length === 0" @click="clickStartMore">批量开启</el-button>
						<el-button :disabled="multipleSelection.length === 0" @click="clickCloseMore">批量关闭</el-button>
					</div>
				</template>
				<el-table-column :width="300" label="操作">
					<template #default="{ row, column, $index }">
						<i-btns :btns="getBtns(row)" :more-number="5" :data="row"></i-btns>
						<el-popover class="box-item" title="" placement="bottom" v-model:visible="row.popoverVisible" :trigger="'click'">
							<template #reference>
								<i-btns
									v-if="getBtns2(row) && getBtns2(row).length"
									style="margin-left: 8px"
									:btns="getBtns2(row)"
									:more-number="5"
									:data="row"
								></i-btns>
							</template>
							<template #default>
								<div>
									<!-- <el-select @change="clickBindNumber" v-model="selectedValue" placeholder="请选择">
										<el-option v-for="item in teamListOptions" :key="item.value" :label="item.label" :value="item.value">
										</el-option>
									</el-select> -->
									<el-select
										v-model="selectedValue"
										filterable
										remote
										reserve-keyword
										placeholder="请输入终端编号"
										remote-show-suffix
										:remote-method="remoteMethod"
										:loading="loading"
										@change="clickBindNumber"
									>
										<el-option v-for="item in teamListOptions" :key="item.value" :label="item.label" :value="item.value" />
									</el-select>
								</div>
							</template>
						</el-popover>
					</template>
				</el-table-column>
			</common-table>
		</div>
		<AddCenterDialog
			:rowData="activeRow"
			:type="dialogType"
			v-model:visible="openBaseDialog"
			@saveForm="refreshTableData"
		></AddCenterDialog>
		<!-- <GuijiDialog :rowData="activeRow" :show="openGuijiDialog" v-model:visible="openGuijiDialog"></GuijiDialog> -->
	</container-lcr>
</template>

<script lang="ts" setup>
import { cloneDeep } from 'lodash-es';
import { onMounted, ref } from 'vue';
import { Delete, Upload, Plus, Download, Refresh } from '@element-plus/icons-vue';
import {
	api_bind,
	api_delete,
	api_getList,
	api_import,
	api_status,
	api_unbind,
	api_getBindDeviceList,
	api_batchDelete,
} from './api/index';
import { EquipmentBaseDTO, SaveEquipment } from './api/types';
import AddCenterDialog from './components/addCenterDialog.vue';
import { XMLHttpDownload2 } from '@/utils/axios';
import { commonConfirm, exportToExcelWithCustomHeaders } from '@/utils';
import { api_getDict } from '@/api/dict';
import { storeUserInfo } from '@/store';
import { ro } from 'element-plus/es/locale';
// import GuijiDialog from './components/guijiDialog.vue';
interface aaa {
	label: string;
	value: string;
}
// const popoverVisible = ref<boolean>(false);
const teamListOptions = ref<aaa[]>([]);
const selectedValue = ref<string>();
const columnsCommon: CommonTableColumn<EquipmentBaseDTO>[] = [
	{
		label: '设备编号',
		prop: 'deviceNumber',
	},
	{
		label: '林机类型',
		prop: 'equipmentCode',
	},
	{
		label: '服务类型',
		prop: 'jobType',
		formatter(row, column, cellValue, index) {
			let str = '';
			let typeValue = cellValue.split(',');
			typeValue.forEach((r: string, index: number) => {
				center_service_type.value.forEach((d) => {
					if (d.code == r) str += d.name;
					if (index != typeValue.length - 1 && d.code == r) str += '、';
				});
			});
			return str;
		},
	},
	{
		label: '归属服务队',
		prop: 'ownerName',
	},
	{
		label: '启用状态',
		prop: 'status',
		slotName: 'slotName1', // 自定义渲染内容
	},
];
const tableData = ref<EquipmentBaseDTO[]>([]);

const pageTotal = ref(0);
const activeRow = ref<EquipmentBaseDTO | null>(null);
// const btns = computed<OptionBtn<EquipmentBaseDTO>[]>(() => {
function getBtns(row2) {
	let temp = [
		{
			label: '详情',
			onClick(row) {
				dialogType.value = 'view';
				activeRow.value = row;
				openBaseDialog.value = true;
			},
		},
		{
			label: '编辑',
			onClick(row) {
				dialogType.value = 'edit';
				activeRow.value = row;
				openBaseDialog.value = true;
			},
		},
		{
			label: '解绑终端',
			tips: '确定解绑终端吗？',
			hide: (row) => {
				return row.bindStatus == 0;
			},
			onClick(row) {
				activeRow.value = row;
				commonConfirm('解绑后将删除该林机设备的所有历史轨迹等数据，请确认后解绑！', '解绑终端').then(() => {
					api_unbind(row.uuid).then((res) => {
						refTable.value?.refreshList();
					});
				});
			},
		},
		{
			label: '轨迹',
			hide: (row) => {
				return !(row.bindStatus == 1 && row.ciotDataType == 1);
			},
			onClick(row) {
				activeRow.value = row;
				openGuijiDialog.value = true;
			},
		},
		{
			label: '实时监控',
			hide: (row) => {
				return !(row.bindStatus == 1 && row.ciotDataType == 2);
			},
			onClick(row) {
				activeRow.value = row;
			},
		},
		{
			label: '删除',
			tips: '是否确定要删除所选数据？',
			onClick(row) {
				activeRow.value = row;
				// commonConfirm('删除后将删除该林机设备的所有历史轨迹等数据，请确认后删除！', '删除').then(() => {
				api_batchDelete({ uuids: row.uuid! }).then((res) => {
					ElMessage.success('操作成功');
					refTable.value?.refreshList();
				});
				// });
			},
		},
	];

	return temp.filter((item) => typeof item.hide !== 'function' || !item.hide(row2));
}
// const btns2 = computed<OptionBtn<EquipmentBaseDTO>[]>(() => {
function getBtns2(row2) {
	let temp = [
		{
			label: '绑定终端',
			hide: (row) => {
				return row.bindStatus == 1;
			},
			onClick(row) {
				row.popoverVisible = true;
				activeRow.value = row;
			},
		},
	];
	return temp.filter((item) => typeof item.hide !== 'function' || !item.hide(row2));
}

const refTable = ref<CommonTableInstance>();
function getList(param?: CommonTableParams) {
	let data = {
		pageNo: param?.pageNumber || 1,
		pageSize: param?.pageSize || 20,
		deviceNumber: formState.value.deviceNumber, // 	* 名称
		equipmentCode: formState.value.equipmentCode,
		tenantId: storeUserInfo.enterpriseId,
	};
	return api_getList(data).then((info) => {
		tableData.value = info.list;
		pageTotal.value = info.total;
	});
}

const center_service_type = ref<DicDataItemCode[]>([]);
onMounted(() => {
	api_getDict('center_service_type').then((res) => {
		center_service_type.value = res;
	});

	refTable.value?.refreshList();
});
function clickSearchBtn() {
	refTable.value?.refreshList();
}
function clickResetBtn() {
	formState.value = cloneDeep(defaultForm);
	refTable.value?.refreshList();
}
const refContainer = ref<ContainerLcrInstance>();

interface quereType {
	deviceNumber: string;
	equipmentCode: string;
	userTelephone: string;
}
const refForm = ref<ElFormInstance>();
const defaultForm: quereType = {
	deviceNumber: '', // 	* 名称
	equipmentCode: '',
	userTelephone: '',
};
const formState = ref<quereType>(cloneDeep(defaultForm));
const openGuijiDialog = ref<boolean>(false); //是否打开轨迹弹窗
const openBaseDialog = ref<boolean>(false); //是否打开新增弹窗
const dialogType = ref('add');
function clickAddCenterBtn() {
	dialogType.value = 'add';
	openBaseDialog.value = true;
}
function refreshTableData() {
	refTable.value?.refreshList();
}
const multipleSelection = ref<EquipmentBaseDTO[]>([]);
function onChangeSelection(rows: EquipmentBaseDTO[]) {
	multipleSelection.value = rows;
}

function clickDeleteMore() {
	let uuids = '';
	multipleSelection.value?.forEach((row, index) => {
		uuids += row.uuid;
		if (index != multipleSelection.value.length - 1) {
			uuids += ',';
		}
	});
	api_batchDelete({ uuids }).then((res) => {
		ElMessage.success('操作成功');
		refTable.value?.refreshList();
	});
}
function uploadBefore(files: File[]) {
	console.log(files);
	if (files && files.length) {
		let file = files[0];
		api_import(file).then(() => {
			refTable.value?.resetList();
		});
	}
	return Promise.reject(false);
}
function getImportTemeplete() {
	XMLHttpDownload2('/web-api/file/v1/template?fileName=machinery.xlsx', {
		fileName: '服务中心导入模板.xlsx',
	}).then((res) => {
		console.log(res);
	});
}

function clickExportTable() {
	// 使用示例
	const headers = {
		deviceNumber: '设备编号',
		equipmentCode: '林机类型',
		jobType: '服务类型',
		ownerName: '设备归属',
		status2: '启用状态',
	};

	let exportArr = multipleSelection.value.length == 0 ? tableData.value : multipleSelection.value;
	let data = exportArr.map((d) => {
		let str = '';
		let typeValue = d.jobType!.split(',');
		typeValue.forEach((r: string, index: number) => {
			center_service_type.value.forEach((d) => {
				if (d.code == r) str += d.name;
				if (index != typeValue.length - 1 && d.code == r) str += '、';
			});
		});
		d.jobType = str;
		d.status2 = d.status == 0 ? '关闭' : '开启';
		return d;
	});
	exportToExcelWithCustomHeaders(data, headers, '机具列表.xlsx', {
		columnWidths: [20, 20, 20, 40, 10],
	});
}

function clickStartMore() {
	api_status({
		uuids: multipleSelection.value.map((d) => {
			return d.uuid;
		}),
		status: 1,
	}).then((res) => {
		refTable.value?.refreshList();
	});
}
function clickCloseMore() {
	api_status({
		uuids: multipleSelection.value.map((d) => {
			return d.uuid;
		}),
		status: 0,
	}).then((res) => {
		refTable.value?.refreshList();
	});
}

function clickBindNumber() {
	console.log(selectedValue.value);
	// popoverVisible.value = false;
	activeRow.value!.popoverVisible = false;
	commonConfirm('请核对所绑定设备无误，确认后绑定！', '绑定终端').then((res) => {
		api_bind(activeRow.value!.uuid, selectedValue.value!).then((res) => {
			selectedValue.value = '';
			refTable.value?.refreshList();
		});
	});
}
const loading = ref(false);
const remoteMethod = (query: string) => {
	loading.value = true;
	api_getBindDeviceList(query, storeUserInfo.enterpriseId)
		.then((res) => {
			loading.value = false;
			console.log('返回设备列表', res);
			teamListOptions.value = (res || []).map((d) => {
				return {
					value: d.deviceNumber,
					label: d.productName,
				};
			});
		})
		.catch((e) => {
			loading.value = false;
		});
};
function handleStatusChange(val, row: EquipmentBaseDTO) {
	console.log(val, row);
	if (row.uuid)
		api_status({
			uuids: [row.uuid],
			status: val,
		}).then((res) => {
			refTable.value?.refreshList();
		});
}
</script>
<style lang="scss" scoped>
.query-box {
	display: flex;
	padding: 16px;
	box-sizing: border-box;
}
</style>
