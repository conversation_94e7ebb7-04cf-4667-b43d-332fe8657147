<template>
	<container-lcr ref="refContainer" :title="false" :topHeight="64">
		<template #top>
			<el-button style="margin-left: 16px" type="primary" @click="clickSearchBtn">新增</el-button>
		</template>
		<div class="table-box-wrap">
			<common-table
				:otherHeight="376"
				ref="refTable"
				:columns="columnsCommon"
				:getList="getList"
				:total="pageTotal"
				:data="tableData"
				:operateWidth="120"
				:get-btns="btns"
				:hide-header="true"
			>
			</common-table>
		</div>
		<AddCenterDialog
			:rowData="activeRow"
			:type="dialogType"
			v-model:visible="openBaseDialog"
			@saveForm="refreshTableData"
		></AddCenterDialog>
	</container-lcr>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { api_getList } from './api/index';
import { JobStatisticsBaseVO } from './api/types';
import AddCenterDialog from './components/addCenterDialog.vue';
import { api_getDict } from '@/api/dict';
import { defaultText } from '@/config';
const columnsCommon: CommonTableColumn<JobStatisticsBaseVO>[] = [
	{
		label: '时段',
		prop: 'type',
		formatter(row, column, cellValue, index) {
			switch (cellValue) {
				case 0:
					return '七天';
				case 1:
					return '近一个月';
				case 2:
					return '本年';
				case 3:
					return '全量数据';
				default:
					return defaultText;
			}
		},
	},
	{
		label: '区域总面积（亩）',
		prop: 'areaArea',
	},
	{
		label: '区域完成总面积（亩）',
		prop: 'finishedArea',
	},
	{
		label: '机械作业面积（亩）',
		prop: 'mechanicalWorkingArea',
	},
	{
		label: '机械化占比%',
		prop: 'mechanicalWorkingArea',
		formatter(row, column, cellValue, index) {
			let str = Number((cellValue / row.areaArea!).toFixed(0)) * 100;
			return str;
		},
	},
	{
		label: '区域作业进度%',
		prop: 'finishedArea',
		formatter(row, column, cellValue, index) {
			let str = Number((cellValue / row.areaArea!).toFixed(0)) * 100;
			return str;
		},
	},
];
const tableData = ref<JobStatisticsBaseVO[]>([]);

const pageTotal = ref(0);
const activeRow = ref<JobStatisticsBaseVO | null>(null);
const btns: OptionBtn<JobStatisticsBaseVO>[] = [
	{
		label: '编辑',
		onClick(row) {
			dialogType.value = 'edit';
			activeRow.value = row;
			openBaseDialog.value = true;
		},
	},
];

const refTable = ref<CommonTableInstance>();
function getList(param?: CommonTableParams) {
	let data = {
		pageNo: param?.pageNumber || 1,
		pageSize: param?.pageSize || 20,
	};
	return api_getList(data).then((info) => {
		tableData.value = info.list;
		pageTotal.value = info.total;
	});
}

const center_service_type = ref<DicDataItemCode[]>([]);
onMounted(() => {
	api_getDict('weatherAlarmType').then((res) => {
		center_service_type.value = res;
	});
	refTable.value?.refreshList();
});
function clickSearchBtn() {
	dialogType.value = 'add';
	openBaseDialog.value = true;
}

const refContainer = ref<ContainerLcrInstance>();
const openBaseDialog = ref<boolean>(false); //是否打开新增弹窗
const dialogType = ref('add');

function refreshTableData() {
	refTable.value?.refreshList();
}
</script>
<style lang="scss" scoped>
.query-box {
	display: flex;
	padding: 16px;
	box-sizing: border-box;
}
</style>
