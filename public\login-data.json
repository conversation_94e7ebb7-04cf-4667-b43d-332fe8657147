{"code": 0, "data": [{"user": {"departmentName": "", "tenantType": 0, "departmentExternalId": "", "rootDepartmentExternalId": "", "departmentId": "", "fullOrganizationPath": "root_organization_tenant_wscp/612c9b0cdc0e820001c3ef2b/612c9b24dc0e820001c3ef2f/636371044cedfd0007f42fbb", "telephone": "***********", "enterpriseExternalId": "636371044cedfd0007f42fbc", "number": "636371044cedfd0007f42fbd", "rootDepartmentName": "", "name": "姬同凯", "rootDepartmentId": "", "userExternalId": "636371044cedfd0007f42fbd", "enterpriseId": "631046874cedfd0007df0758", "enterpriseName": "姬同凯团队运营", "tenantExtend": {"tenantType": 0, "tenantCategory": "084ab14529c711ed97080242ac11000e", "accountIdentity": "tenant"}, "username": "***********"}, "routers": [], "authorities": [], "token": {"access_token": "bearer ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "expires_in": 7199}}], "message": "操作成功!"}