import http, { resolveFunc } from '@/utils/axios';
import { getTokenPromise } from '@/utils/token';
import { envConfig, isDev, maxUploadPartSize, maxUploadPartStepSize } from '@/config';

declare global {
	interface RequestFileUpload {
		fileCategory: string; // " ",
		fileFolder: string; // " ",
	}

	// 上传成功后接口返回的信息
	interface ResponseFile {
		fileAccessPath: string; //  "/file/0/6662ca65713c09000731fc4c.png",
		fileCreateTime: string; //  "2024-06-07 16:52:53",
		fileName: string; //  "icon14",
		fileSize: number; //  11856,
		fileStoragePath: string; //  "/file/0/6662ca65713c09000731fc4c.png",
		fileType: string; //  "png",
		fileUuid: string; //  "6662ca65713c09000731fc4c"
	}

	interface ResponseFilePart extends ResponseFile {
		chunkInfo: {
			chunk: number; // 当前分片[从0开始]
			chunks: number; // 分片总数
		};
	}

	interface ResponseUpload {
		file: ResponseFile;
		thumbnail: ResponseFile | null;
	}

	interface ResponseUploadPart {
		file: ResponseFilePart;
		thumbnail: ResponseFile | null;
	}
}
const ApiCommon = {
	fileUpload: '/open-api/v1/file/upload', // 上传文件接口 带缩略图
	fileUploadThumbnail: '/open-api/v1/file/upload/for/thumbnail', // 上传文件接口 带缩略图
	fileUploadPart: '/open-api/v1/file/part/upload/for/thumbnail', // 上传文件接口 分片上传 带缩略图
	getFileUuid: '/open-api/v1/file/generate/uuid', // 生成文件唯一标识 分片上传前调用
};
interface FileUploadParam {
	chunk?: number; // 当前分片[从0开始]
	chunks?: number; // 分片总数
	fileName?: string;
	fileUuid?: string;
	token?: string;
	[key: string]: any;
}
const tokenKey = 'Authorization';
const disablePrat = true;
// 上传 文件
export function api_fileUpload(
	file: File,
	param?: FileUploadParam,
	progress?: Function,
): Promise<ResponseUploadPart | ResponseUpload> {
	return Promise.resolve()
		.then(() => {
			if (param?.token) {
				return Promise.resolve(param.token);
			}
			return getTokenPromise();
		})
		.then((token) => {
			// 此项目中，默认禁用分段上传
			progress = progress || function () {};
			const partMaxSize = maxUploadPartSize * 1024 * 1024; // 超过这个大小，分片上传
			// 如果禁用分片上传，或者 小于预设大小，不用分片，直接上传
			if (disablePrat || file.size <= partMaxSize) {
				// 上传过程中，每隔一会儿 触发一次 progress
				let progress_ = 0; // 进度
				let timer = setInterval(() => {
					progress!(progress_);
					if (progress_ <= 85) {
						// 假的进度条，最大为95
						progress_ += 10;
					}
				}, 300);
				let url: string = envConfig.VITE_BASE_FILE_API + ApiCommon.fileUpload;
				// if (isDev) {
				// 	url = '/file_api' + ApiCommon.fileUpload;
				// }
				return uploadXML(url, file, token, param).finally(() => {
					// 上传成功后，清空定时器
					clearInterval(timer);
					progress!(100);
				});
			}
			// 需要分片
			return uploadPart(file, token, progress);
		});
}

//   分片上传
// 先通过接口获取分片上传标识
// 前端将文件分片，切割
function uploadPart(file: File, token: string, progress?: Function): Promise<ResponseUploadPart | ResponseUpload> {
	progress = progress || function () {};
	let url: string = envConfig.VITE_BASE_FILE_API + ApiCommon.getFileUuid;
	// if (isDev) {
	// 	url = '/file_api' + ApiCommon.getFileUuid;
	// }
	return http
		.get(url, null, {
			fullPath: true,
			useToken: false,
			config: {
				headers: {
					[tokenKey]: token,
				},
			},
		})
		.then((ref: any) => {
			let fileUuid = ref?.[0];
			if (fileUuid) {
				return fileUuid;
			}
			return Promise.reject(getRejectObj('获取文件id失败'));
		})
		.then((fileUuid: string) => {
			return new Promise((resolve, reject) => {
				let urlPart: string = envConfig.VITE_BASE_FILE_API + ApiCommon.fileUploadPart;
				// if (isDev) {
				// 	urlPart = '/file_api' + ApiCommon.fileUploadPart;
				// }
				let generator: Generator | null = uploadPartFor(urlPart, file, token, { fileUuid: fileUuid }, callBack);
				generator.next(); // 先调用一次，开始上传
				function callBack(rel: UploadPartForCallBack) {
					// 最后一次时， chunk 比 chunks 少1
					if (rel.chunk < rel.chunks - 1) {
						if (rel.state === 1) {
							const num = Math.floor(((rel.chunk + 1) / rel.chunks) * 10000) / 100;
							progress!(num);
							generator!.next();
						} else {
							generator = null;
							reject(rel.rel);
						}
					} else {
						// 所有分片上传完成
						progress!(100);
						resolve(rel.rel);
						generator = null;
					}
				}
			});
		});
}

interface UploadPartForCallBack {
	state: number; // 1 成功 0 失败
	rel: any; // 可能是错误信息，也可能是上传接口返回的文件信息
	chunk: number; // 当前分片
	chunks: number; // 总分片
}
function* uploadPartFor(url: string, file: File, token: string, param: FileUploadParam, callBack: Function) {
	const partStepSize = maxUploadPartStepSize * 1024 * 1024; // 每片传多大
	const chunks = Math.ceil(file.size / partStepSize); // 分几段
	const fileName = file.name;
	for (let i = 0; i < chunks; i++) {
		const filePart = file.slice(i * partStepSize, (i + 1) * partStepSize);
		console.log('11111111111 filePart', filePart);
		const param_: FileUploadParam = Object.assign({}, param, {
			chunk: i,
			chunks: chunks,
			fileName: fileName,
			// fileUuid:fileUuid // 在 param 携带的有
		});
		uploadXML(url, filePart, token, param_)
			.then((info) => {
				// const {} = info as ResponseUploadPart;
				const rel: UploadPartForCallBack = {
					state: 1,
					rel: info,
					chunk: i,
					chunks: chunks,
				};
				callBack(rel);
			})
			.catch((err) => {
				const rel: UploadPartForCallBack = {
					state: 0,
					rel: err,
					chunk: i,
					chunks: chunks,
				};
				callBack(rel);
			});
		yield; // 暂停标志
	}
}
// 真实的上传 url 中不用传入 Origin
function uploadXML(
	url: string,
	file: File | Blob,
	token: string,
	param?: FileUploadParam,
): Promise<ResponseUploadPart | ResponseUpload> {
	// return resolveFunc({
	// 	file: {
	// 		fileAccessPath: '/file/0/66627731b9d82100089ec155.png',
	// 		fileCreateTime: '2024-06-07 10:57:53',
	// 		fileName: 'icon11',
	// 		fileSize: 11957,
	// 		fileType: 'png',
	// 		fileUuid: '66627731b9d82100089ec155',
	// 	},
	// 	thumbnail: null,
	// });

	return new Promise((resolve, reject) => {
		const formData = new FormData();
		formData.append('file', file);
		param = param || {};
		for (let key in param) {
			const val = (param as any)[key];
			formData.append(key, val);
		}
		const xhr = new XMLHttpRequest();
		// if (isDev) {
		// 	xhr.timeout = 2 * 1000;
		// }
		// if (isDev) {
		// 	url = 'http://172.16.30.81/basic-service/open-api/v1/file/upload';
		// }
		xhr.open('POST', url);
		// xhr.setRequestHeader('Content-type', 'application/json;charset=UTF-8');
		xhr.setRequestHeader(tokenKey, token);
		xhr.responseType = 'json';
		xhr.onload = function () {
			console.log('文件上传 接口返回数据了', xhr);
			if (xhr.readyState === 4 && xhr.status === 200) {
				const { code, data, message } = xhr.response;
				if (code === 0) {
					// 临时处理// 处理成带有缩略图结构的数据
					resolve({
						file: data[0],
						thumbnail: null,
					});
					// resolve(data[0]);
				} else {
					reject({
						code: -1,
						message: message || '上传失败',
					});
				}
			} else {
				reject({
					code: -1,
					message: '上传失败',
				});
			}
		};
		// xhr.onloadend = function () {
		// 	console.log('11111111 onloadend');
		// };
		xhr.onabort = function () {
			console.log('11111111 onabort', xhr);
			reject({
				code: -1,
				message: '上传中断',
			});
		};
		xhr.onerror = function () {
			console.log('11111111 onerror');
			reject({
				code: -1,
				message: '上传失败',
			});
		};
		xhr.ontimeout = function () {
			console.log('11111111 ontimeout');
			reject({
				code: -1,
				message: '上传超时',
			});
		};
		xhr.send(formData);
		// if (isDev) {
		// setTimeout(() => {
		// 	xhr.abort();
		// }, 2000);
		// }
	});
}
function getRejectObj(msg: string, code: number = -1) {
	return {
		code: code,
		message: msg || '失败!',
	};
}
