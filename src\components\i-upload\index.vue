<template>
	<div class="i-upload" :class="disabled ? 't-disabled' : ''" :id="'id_upload_' + randomId" @click.stop="onTrigger">
		<input
			class="i-input"
			ref="refInput"
			:key="refreshKey"
			:disabled="disabled"
			type="file"
			@change="onInputChange"
			:multiple="multiple"
			:accept="accept_"
		/>
		<slot> </slot>
	</div>
</template>

<script lang="tsx" setup>
import { api_fileUpload } from '@/api/file-upload';
import { computed, onMounted, ref } from 'vue';
import { beforeUpload_, showResultTips, showValidateTips } from './utils';
export interface Props {
	beforeUpload?: (files: File[]) => Promise<boolean>;
	accept?: string | string[]; // 文件格式 string 格式是原生格式， string[]是自定义格式，需要转成原生格式
	fileSize?: number; // 文件限制大小 单位 M
	multiple?: boolean; // 多选
	resultTips?: boolean; // 上传结果提示，成功失败都会提示
	errorTips?: boolean; // 上传失败时提示，只要有一个文件失败就会提示 优先级  errorTips > resultTips
	successTips?: boolean; // 全部上传成功时提示  优先级  successTips > resultTips
	drop?: boolean; // 拖拽
	disabled?: boolean; // 禁用
	loadingText?: string; // loading提示文案
	apiParam?: RequestFileUpload; // 上传到接口中的参数
}
const props = withDefaults(defineProps<Props>(), {
	multiple: true,
	errorTips: undefined,
	successTips: undefined,
	resultTips: true,
	drop: false,
	disabled: undefined,
	loadingText: '正在上传中，请勿关闭页面。',
});
const emit = defineEmits<{
	(ev: 'uploadBefore', files: FileList): void;
	(ev: 'change', info: EventUploadChange): void;
}>();
const refInput = ref<HTMLInputElement>();
const refreshKey = ref(0); // 用于刷新input  清空已选择的文件，不然选择重复的文件时，不会触发事件，每次 +1
let fileList: File[] = [];
let fileListObj_success: Array<ResponseUpload> = []; // 上传成功的
let fileListObj_error: UploadErrorFile[] = []; // 上传失败的

const accept_ = computed(() => {
	if (!props.accept) return undefined;
	if (typeof props.accept === 'string') {
		return props.accept;
	} else if (props.accept?.length) {
		return props.accept.map((item) => '.' + item).join(' ,');
	}
	return undefined;
});
const fileSize_ = computed(() => {
	if (!props.fileSize) return undefined;
	return props.fileSize * 1024 * 1024;
});
const randomId = Math.floor(Math.random() * 1000000) + new Date().getTime();

// input 上传事件发生了变化
function onInputChange(ev: any) {
	console.log('1111111111 上传事件发生了变化 ', ev.target?.files);
	const files = (ev.target?.files as FileList) || [];
	emit('uploadBefore', files);
	let files_: File[] = [];
	for (let index = 0; index < files.length; index++) {
		files_.push(files[index]);
	}
	update1(files_);
	ev.target.value = '';
}
// 点击触发 原生input上传事件
function onTrigger() {
	if (props.disabled) return;
	refInput.value?.click();
}

let fileLength = 0; // 当前上传中的数量
// 上传第一步
function update1(files: File[]) {
	fileList = files;
	fileListObj_success = [];
	fileListObj_error = [];
	console.log('11111111111111', fileList);
	beforeUpload_(files, fileSize_.value, props.accept)
		.then(() => {
			if (props.beforeUpload) {
				return props.beforeUpload(fileList);
			}
			return Promise.resolve(true);
		})
		.then(() => {
			fileLength = fileList.length;
			for (let i = 0; i < fileLength; i++) {
				const file = fileList[i];
				update2(file);
			}
		})
		.catch((err: false | string | string[]) => {
			if (err === false) {
				// 可能是 beforeUpload 中返回 false 取消了上传
			} else {
				console.log('111111111111111 文件校验失败', err);
				fileList = [];
				showValidateTips(err);
			}
		})
		.finally(() => {
			refreshKey.value++;
			// refInput.value?.clearFiles();
		});
}
// 调用接口 上传
function update2(file: File) {
	showLoading();
	api_fileUpload(file, props.apiParam)
		.then((info) => {
			fileListObj_success.push(info);
		})
		.catch((err) => {
			let meg = err?.message || '上传失败';
			if (typeof err === 'string') {
				meg = err;
			}
			fileListObj_error.push({
				fileName: file.name,
				fileSize: file.size,
				fileType: file.type,
				message: meg,
			});
		})
		.finally(() => {
			fileLength -= 1;
			if (fileLength === 0) {
				hideLoading();
				// 上传完成了
				console.log('111111111111111111', fileListObj_success, fileListObj_error);
				emit('change', {
					successFileList: fileListObj_success,
					successFiles: fileList,
					errorFileList: fileListObj_error,
				});
				const errorTips = props.errorTips ?? props.resultTips;
				const successTips = props.successTips ?? props.resultTips;
				showResultTips(successTips, errorTips, fileListObj_success, fileListObj_error);
			}
		});
}

const idLoading = 'id_upload_loading_box';
function createLoading() {
	let loadingDom = document.getElementById(idLoading) as HTMLDivElement | null;
	if (!loadingDom) {
		// 如果不存在，  像页面中添加元素
		loadingDom = document.createElement('div');
		loadingDom.classList.add('g-loading-box', 'i-g-upload-loading-box');
		loadingDom.id = idLoading;
		loadingDom.innerHTML = `
		<div class="i-loading-text">${props.loadingText}</div>
		<div class="g-loading-dot-spin">
			<i class="g-loading-dot-item"></i>
			<i class="g-loading-dot-item"></i>
			<i class="g-loading-dot-item"></i>
			<i class="g-loading-dot-item"></i>
		</div>`;
		document.body.appendChild(loadingDom);
	}
}
function showLoading() {
	// 先显示遮罩，防止用户重复点击，延迟显示背景，避免闪屏问题
	const loadingDom = document.getElementById(idLoading) as HTMLDivElement | null;
	if (!loadingDom) return;
	if (loadingDom.style.opacity !== '0') {
		// 加个判断，防止重复打开
		loadingDom.style.opacity = '0';
		loadingDom.style.display = 'block';
		setTimeout(() => {
			loadingDom.style.opacity = '1';
		}, 10);
	}
}
function hideLoading() {
	const loadingDom = document.getElementById(idLoading) as HTMLDivElement | null;
	if (!loadingDom) return;
	loadingDom.style.display = 'none';
}
onMounted(() => {
	createLoading();
	if (props.drop) {
		const dropBox = document.getElementById('id_upload_' + randomId)!;
		dropBox.addEventListener('dragenter', dragEnter, false);
		dropBox.addEventListener('dragover', dragOver, false);
		dropBox.addEventListener('drop', drop, false);
		function dragEnter(ev: Event) {
			ev.stopPropagation();
			ev.preventDefault();
		}
		function dragOver(ev: Event) {
			ev.stopPropagation();
			ev.preventDefault();
		}
		function drop(ev: DragEvent) {
			// 当文件拖拽到dropBox区域时,可以在该事件取到files
			ev.stopPropagation();
			ev.preventDefault();
			const files = (ev.dataTransfer?.files as FileList) || [];
			if (props.multiple === false && files.length > 1) {
				// 如果是单选，且拖拽的多个文件
				alert('仅能上传单个文件！');
				return;
			}
			// 将 FileList 转换成 File[]
			let files_: File[] = [];
			for (let index = 0; index < files.length; index++) {
				files_.push(files[index]);
			}
			update1(files_);
		}
	}
});
defineExpose({
	upload: onTrigger,
});
</script>
<style lang="scss" scoped>
.i-upload {
	display: inline-block;
	cursor: pointer;
	&.t-disabled {
		cursor: no-drop;
	}
	.i-input {
		display: none;
	}
}
</style>
<style lang="scss">
.i-g-upload-loading-box {
	display: none;
	.g-loading-dot-spin {
		margin: -30px 0 0 -10px;
	}
	> .i-loading-text {
		font-size: 14px;
		position: absolute;
		top: 50%;
		left: 50%;
		text-indent: -50%;
		// text-align: center;
		margin: 0px 0 0 8px;
	}
}
</style>
