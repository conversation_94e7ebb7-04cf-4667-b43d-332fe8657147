## 核心配置模板

nginx.conf

```nginx
user  root;
worker_processes  32;
worker_rlimit_nofile 65535;
worker_priority -20;

error_log  /opt/logs/error.log error;
pid        /var/run/nginx.pid;


events {
    use epoll;
    worker_connections  65535;
    multi_accept on;
}



http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /opt/logs/access.log  main;
    sendfile        on;
    #tcp_nopush     on;
    keepalive_timeout  65;
	client_header_buffer_size 512k;
	client_max_body_size 10m;
	large_client_header_buffers 4 512k;
    #gzip  on;
	gzip on;
    gzip_min_length 1024k;
    gzip_comp_level 1;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript;
    gzip_static on;
    gzip_vary on; 
    gzip_buffers 2 4k;
    gzip_http_version 1.1;

	
	 # idaas
	upstream idaas-service{
		server ***********:18080  fail_timeout=10s max_fails=1;
	}
	
	upstream idaas-client{
		server ***********:18081  fail_timeout=10s max_fails=1;
	}
	
	upstream idaas-sso{
		server ***********:18082  fail_timeout=10s max_fails=1;
	}

	# iip



	# idaas for demo end 
	

	# 工互网

	upstream sr-cms-service{
		server ***********:8130  fail_timeout=10s max_fails=1;
	}

	upstream sr-client-knowledge{
           server ***********:8131  fail_timeout=10s max_fails=1;
     }

     upstream gyhl-sr-cmsi-service{
         server ***********:8132  fail_timeout=10s max_fails=1;
      }

	upstream sr-client-news{
		server ***********:8133  fail_timeout=10s max_fails=1;
	}


	upstream sr-middleware-service{
		server ***********:8134  fail_timeout=10s max_fails=1;

	}

	upstream common-file-service{
		server ***********:8135  fail_timeout=10s max_fails=1;

	}
	
	upstream  iip-gateway{
		server ***********:8136  fail_timeout=10s max_fails=1;
	}


	upstream  uniformly-call-services{
		server ***********:8137  fail_timeout=10s max_fails=1;
	}

	upstream  iip-operate-cas-client{
		server ***********:8138  fail_timeout=10s max_fails=1;
	}
	
 	upstream  iip-isv-cas-client{
		server ***********:8139  fail_timeout=10s max_fails=1;
	}
	

	upstream  iip-tenant-cas-client{
		server ***********:8140  fail_timeout=10s max_fails=1;
	}
    

	upstream  iip-service{
		server ***********:8141  fail_timeout=10s max_fails=1;
	}
	
	upstream idaas-sso-operate{
		server ***********:8142  fail_timeout=10s max_fails=1;
	}
	
	upstream idaas-sso-isv{
		server ***********:8143  fail_timeout=10s max_fails=1;
	}

	upstream idaas-sso-tenant{
		server ***********:8144  fail_timeout=10s max_fails=1;
	}

	# iip end


	# ciot 

		upstream iip-ciot-service{
     	 	server ***********:9090  fail_timeout=10s max_fails=1;
 	 }

	upstream ciot-basic{
		server ***********:9089  fail_timeout=10s max_fails=1;
	}

  	upstream  iip-ciot-operate-cas-client{
      		server ***********:9087  fail_timeout=10s max_fails=1;
  	}

  	upstream  iip-ciot-tenant-cas-client{
    		server ***********:9088  fail_timeout=10s max_fails=1;
 	 }
	 
	 
 	 # ciot end
	 upstream  forestry-video-service{
    		server ***********:9086  fail_timeout=10s max_fails=1;
 	 }

 	 upstream  cms-service{
    		server ***********:18130  fail_timeout=10s max_fails=1;
 	 }
	
    include /etc/nginx/conf.d/*.conf;
}

```

80.conf

    server {
    listen       80;
    server_name  ***********;
	location /manager {
		deny  all;
	}

	location ^~ /idaas-service/api {
		deny  all;
	}

	
	location ^~ /idaas{ 
		root /opt/static;
		index index.html;
		try_files $uri $uri/ /idaas/index.html;
	} 


	location ^~ /idaas-prod-file { 
		root /opt/fileupload/idaas; 
	}


	location ^~ /cms{ 
		root /opt/static;
		index index.html;
		try_files $uri $uri/ /cms/index.html;
	}

	location ^~ /image {
		root /opt;
		add_header 'Access-Control-Allow-Origin' '*';
	}




  	 location /iip-service/download/ {
		default_type  'application/octet-stream';
                        add_header Content-disposition "attachment"; 			
        	alias /opt/static/common/iip-service-download/;
    	}

    	location ^~ /AppInstallFile{ 
		root /opt/app_install; 
	}


    location  /file-security-common/ {
    internal;
    proxy_pass http://forestry-file.huabei-2.zos.ctyun.cn/;
    proxy_set_header Authorization "";
    proxy_hide_header Content-Disposition;
    }
    location ~* ^/common-file-security/(.*)\.(png|jpg|gif|swf|ico|jpeg|HEIC|tif|bmp|avi|mp4|mov|wmv|flv|rmvb|mpg|mpeg|mkv|rm|asf|mpe|vob|mp3|m4a|wav|ogg|cd|midi|flac|ape|alac) {
    rewrite ^/common-file-security/(.*)$ /$1 break;
    proxy_set_header Authorization "";
    proxy_pass http://forestry-file.huabei-2.zos.ctyun.cn;
    proxy_hide_header Content-Disposition;
    }
    location  /commmon-file-service/{
    proxy_pass http://common-file-service/;
    proxy_redirect default;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $host:$server_port;
    client_max_body_size 100m;
    client_body_buffer_size 128k;
    proxy_connect_timeout 900;
    proxy_send_timeout 900;
    proxy_read_timeout 900;
    proxy_buffer_size 4k;
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 64k;
    proxy_temp_file_write_size 64k;
    fastcgi_buffers 8 128k;
    }


        	location  /{
            if ($request_filename ~* .*\.(?:htm|html)$)  ## .....................html...htm...............
         {
            add_header Cache-Control "no-cache";
         }
          if ($request_uri  ~* "\.\.") {
                  return 404;
                   }
        root /opt/static/iip/portal;
           index index.html;
           try_files $uri $uri/ /index.html;
   	}

    #	location ^~ /wscp-file {
    #		root /opt/fileupload/wscp;
    #		proxy_pass http://***********;
    #	   add_header Access-Control-Allow-Origin *;
    #        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    #        add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    #        if ($request_method = 'OPTIONS') {
    #            add_header Access-Control-Allow-Origin *;
    #            add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    #            add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    #            add_header Access-Control-Max-Age 1728000;
    #            add_header Content-Type text/plain;
    #            add_header Content-Length 0;
    #            return 204;
    #        }
    #	}


	location ^~ /news-manage{
		root /opt/static;
		index index.html;
		try_files $uri $uri/ /news-manage/index.html;
	}

	location ^~ /iip-tenant/app-center{
		if ($request_filename ~* .*\.(?:htm|html|js)$)  ## .....................html...htm...............
            {
              add_header Cache-Control "no-cache";
              }
		root /opt/static;
		index index.html;
		try_files $uri $uri/ /iip-tenant/app-center/index.html;
	}

	location ^~ /iip-isv/open-service{
		if ($request_filename ~* .*\.(?:htm|html|js)$)  ## .....................html...htm...............
            {
              add_header Cache-Control "no-cache";
              }
		root /opt/static;
		index index.html;
		try_files $uri $uri/ /iip-isv/open-service/index.html;
		}
	 
	location ^~ /iip-isv-management{
		if ($request_filename ~* .*\.(?:htm|html|js)$)  ## .....................html...htm...............
            {
              add_header Cache-Control "no-cache";
              }
		root /opt/static;
		index index.html;
		try_files $uri $uri/ /iip-isv-management/index.html;
	} 
	location ^~ /iip-isv-developer{
		if ($request_filename ~* .*\.(?:htm|html|js)$)  ## .....................html...htm...............
            {
              add_header Cache-Control "no-cache";
              }
		root /opt/static;
		index index.html;
		try_files $uri $uri/ /iip-isv-developer/index.html;
	}
	location ^~ /iip-tenant-management{
		root /opt/static;
		index index.html;
		try_files $uri $uri/ /iip-tenant-management/index.html;
	}

	location ^~ /tenant-console{
               if ($request_filename ~* .*\.(?:htm|html|js)$)  ## .....................html...htm...............
            {
              add_header Cache-Control "no-cache";
              }
		root /opt/static;
                index index.html;
                try_files $uri $uri/ /tenant-console/index.html;
  	}
	
	location ^~ /iip-operate-management{
		if ($request_filename ~* .*\.(?:htm|html|js)$)  ## .....................html...htm...............
            {
              add_header Cache-Control "no-cache";
              }
		root /opt/static;
		index index.html;
		try_files $uri $uri/ /iip-operate-management/index.html;
	}

	location ^~ /knowledge-manage{
           root /opt/static;
           index index.html;
           try_files $uri $uri/ /knowledge-manage/index.html;
    }


    location ^~ /iip-ciot{
    if ($request_filename ~* .*\.(?:htm|html)$)  ## 配置页面不缓存html和htm结尾的文件
    {
    add_header Cache-Control "no-cache";
    }
    root /opt/static;
    index index.html;
    try_files $uri $uri/ /iip-ciot/index.html;
    }

	location ^~ /iip-ciot-tenant{
		 if ($request_filename ~* .*\.(?:htm|html)$)  ## 配置页面不缓存html和htm结尾的文件
	      {
	         add_header Cache-Control "no-cache";
	      }
		root /opt/static;
		index index.html;
		try_files $uri $uri/ /iip-ciot-tenant/index.html;
	}




	
        location ^~ /ljy-web {
                root /opt/static;
                index index.html;
                try_files $uri $uri/ /ljy-web/index.html;
        }

	location ^~ /ljy-screen {
                root /opt/static;
                index index.html;
                try_files $uri $uri/ /ljy-screen/index.html;
        }



    # front end



	location /idaas-service{
		proxy_pass http://idaas-service;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}
	
	location /idaas-client{
		proxy_pass http://idaas-client;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}
	
	location /sso{
		proxy_pass http://idaas-sso;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}

    #idaas service end


	#iip service

	location /iip-operate-idaas-client{
		proxy_pass http://idaas-client;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}
	
	location /iip-operate-sso{
		proxy_pass http://idaas-sso-operate;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}
	
	location /iip-isv-sso{
		proxy_pass http://idaas-sso-isv;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}
	
	location /iip-tenant-sso{
		proxy_pass http://idaas-sso-tenant;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}


	location /iip-tenant-sr-news-client/{
		proxy_pass http://sr-client-news;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port;client_max_body_size 100m;client_body_buffer_size 128k;
		proxy_connect_timeout 900;
		proxy_send_timeout 900;
		proxy_read_timeout 900;
		proxy_buffer_size 4k;
		proxy_buffers 4 32k;
		proxy_busy_buffers_size 64k;
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}
    location /iip-tenant-sr-knowledge-client/{
    proxy_pass http://sr-client-knowledge;
    proxy_redirect default;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $host:$server_port;client_max_body_size 100m;client_body_buffer_size 128k;
    proxy_connect_timeout 900;
    proxy_send_timeout 900;
    proxy_read_timeout 900;
    proxy_buffer_size 4k;
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 64k;
    proxy_temp_file_write_size 64k;
    fastcgi_buffers 8 128k;
    }
    # 门户聚合服务
    location /gather-middleware-service/{
    proxy_pass http://***********:8134/;
    proxy_redirect default;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header Host $host:$server_port;
    client_max_body_size 100m;
    client_body_buffer_size 128k;
    proxy_connect_timeout 900;
    proxy_send_timeout 900;
    proxy_read_timeout 900;
    proxy_buffer_size 4k;
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 64k;
    proxy_temp_file_write_size 64k;
    fastcgi_buffers 8 128k;
    }
    



	location /basic-service/ {
		proxy_pass http://***********:8135/;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}



	
	
	location ^~ /uniformly-call-services/{
		proxy_pass http://uniformly-call-services/;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}


	location ^~ /iip-service{
		proxy_pass http://iip-service;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}
	location /iip-gateway/{
		proxy_pass http://iip-gateway/;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}
	location /iip-tenant-cas-client{
		proxy_pass http://iip-tenant-cas-client;
		proxy_redirect default;
		proxy_set_header Origin "http://lcjx.agrimachcloud.com";
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}


	location /iip-isv-cas-client{
		proxy_pass http://iip-isv-cas-client;
		proxy_redirect default;
		proxy_set_header Origin "http://lcjx.agrimachcloud.com";
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}

	
	location /iip-operate-cas-client{
		proxy_pass http://iip-operate-cas-client;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
    #		proxy_set_header Origin "http://lcjx.agrimachcloud.com";
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}
	
	#iip service end

	# ciot

	# ws代理
	location /ciot-basic/ {
	    proxy_pass http://***********:9085/;
	    proxy_http_version 1.1;
	    proxy_set_header Upgrade $http_upgrade;
	    proxy_set_header Connection "Upgrade";
	    proxy_set_header X-Real-IP $remote_addr;
	    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	    proxy_set_header Host $host;
	    proxy_read_timeout 900;
	    proxy_connect_timeout 900;
	    proxy_send_timeout 900;
	}

    #	location /ciot-basic/{
    #		proxy_pass http://ciot-basic/;
    #		proxy_set_header Upgrade $http_upgrade;
    #		proxy_set_header Connection "Upgrade";
    #		proxy_redirect default;
    #		proxy_set_header X-Real-IP $remote_addr;
    #		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #		proxy_set_header Host $host:$server_port;
    #		client_max_body_size 100m;
    #		client_body_buffer_size 128k;
    #		proxy_connect_timeout 900;
    #		proxy_send_timeout 900;
    #		proxy_read_timeout 900;
    #		proxy_buffer_size 4k;
    #		proxy_buffers 4 32k;
    #		proxy_busy_buffers_size 64k;
    #		proxy_temp_file_write_size 64k;
    #		fastcgi_buffers 8 128k;
    #	}


	location /iip-ciot-operate-cas-client{
			proxy_pass http://iip-ciot-operate-cas-client;
			proxy_redirect default;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header Host $host:$server_port; 
			client_max_body_size 100m; 
			client_body_buffer_size 128k; 
			proxy_connect_timeout 900;
			proxy_send_timeout 900; 
			proxy_read_timeout 900; 
			proxy_buffer_size 4k; 
			proxy_buffers 4 32k; 
			proxy_busy_buffers_size 64k; 
			proxy_temp_file_write_size 64k;
			fastcgi_buffers 8 128k;
		}


	location /iip-ciot-tenant-cas-client{
			proxy_pass http://iip-ciot-tenant-cas-client;
			proxy_redirect default;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_set_header Host $host:$server_port; 
			client_max_body_size 100m; 
			client_body_buffer_size 128k; 
			proxy_connect_timeout 900;
			proxy_send_timeout 900; 
			proxy_read_timeout 900; 
			proxy_buffer_size 4k; 
			proxy_buffers 4 32k; 
			proxy_busy_buffers_size 64k; 
			proxy_temp_file_write_size 64k;
			fastcgi_buffers 8 128k;
		}


	location /iip-ciot-service/{
		proxy_pass http://iip-ciot-service/;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}
	#ciot end
	
	location /forestry-video-service/{
		proxy_pass http://forestry-video-service/;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}

	location ^~ /cms-service{
		proxy_pass http://cms-service;
		proxy_redirect default;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host:$server_port; 
		client_max_body_size 100m; 
		client_body_buffer_size 128k; 
		proxy_connect_timeout 900;
		proxy_send_timeout 900; 
		proxy_read_timeout 900; 
		proxy_buffer_size 4k; 
		proxy_buffers 4 32k; 
		proxy_busy_buffers_size 64k; 
		proxy_temp_file_write_size 64k;
		fastcgi_buffers 8 128k;
	}

	
	location /lcdd/{
           proxy_pass http://***********:9600/;
           proxy_redirect default;        
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header Host $host:$server_port;
           client_max_body_size 100m;  
           client_body_buffer_size 128k;            
           proxy_connect_timeout 900;  
           proxy_send_timeout 900;                 
           proxy_read_timeout 900;                                     
           proxy_buffer_size 4k;                    
           proxy_buffers 4 32k;                     
           proxy_busy_buffers_size 64k; 
           proxy_temp_file_write_size 64k;          
           fastcgi_buffers 8 128k;                                     
        }

}

