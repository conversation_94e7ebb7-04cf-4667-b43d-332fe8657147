<template>
	<el-dialog
		v-model="dialogVisible"
		width="1000px"
		:title="title"
		:close-on-click-modal="false"
		append-to-body
		align-center
		destroy-on-close
		@closed="closeDialog"
	>
		<el-form
			ref="formRef"
			style="margin: 0 12px"
			:model="formData"
			:disabled="type == 'view'"
			label-width="120px"
			:rules="formRules"
		>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="服务类型" prop="serviceScopeList">
						<el-select v-model="formData.serviceScopeList" multiple placeholder="请选择">
							<el-option v-for="item in center_service_type" :key="item.code" :label="item.name" :value="item.code" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="联系人" prop="contactName">
						<el-input v-model="formData.contactName"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="作业面积" prop="jobArea">
						<el-input v-model="formData.jobArea"> <template #append>亩</template></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="联系方式" prop="contactTelephone">
						<el-input v-model="formData.contactTelephone"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item
						v-for="(equip, index) in formData.jobEquipments"
						class="i-form-item"
						:label="index == 0 ? '所需设备' : ''"
						:key="equip.key"
						:prop="'jobEquipments.' + index + '.equipmentCode'"
						:rules="{
							required: true,
							message: '不能为空',
							trigger: 'blur',
						}"
					>
						<el-input style="width: 45%" v-model="equip.equipmentCode"></el-input>
						<el-input style="width: 45%; margin-left: 10%" v-model="equip.count"><template #append>台</template></el-input>
						<img
							v-if="index == formData.jobEquipments.length - 1"
							class="dialogImg"
							style="position: absolute; top: 0; right: -38px"
							src="@/assets/images/tjlj.png"
							alt=""
							@click="addDomain"
						/>
						<img
							v-if="index != 0 || index != formData.jobEquipments.length - 1"
							class="dialogImg"
							style="position: absolute; top: 0; right: -70px"
							:style="{ right: index != formData.jobEquipments.length - 1 ? '-38px' : '-70px' }"
							src="@/assets/images/sclj.png"
							alt=""
							@click="removeDomain(equip)"
						/>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="14">
					<el-form-item label="所在位置" prop="address">
						<el-cascader
							:props="{
								value: 'areaId',
								label: 'areaName',
								children: 'children',
							}"
							style="width: 100%"
							v-model="formData.address"
							:options="areas"
							@change="handleChange"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="10">
					<el-form-item style="position: relative; margin-left: -135px" label="" prop="addressDetail">
						<el-input @click.native="chooseAddress" v-model="formData.addressDetail" placeholder="请选择详细地址">
							<template #suffix>
								<el-icon @click.native="chooseAddress" class="el-input__icon"><Location /></el-icon> </template
						></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="需求完成时间" prop="finishTime">
						<el-date-picker
							@change="handleTimeChange"
							v-model="formData.finishTime"
							type="date"
							placeholder="请选择需求完成时间"
						/>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="24">
					<el-form-item label="需求详情" prop="jobInfo">
						<el-input v-model="formData.jobInfo"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<el-row justify="center">
			<el-button @click="closeDialog" style="margin-right: 12px">取消</el-button>
			<el-button type="primary" @click="submit" v-if="type !== 'view'" style="margin-right: 12px">确认</el-button>
		</el-row>
		<!-- 地图弹窗 -->
		<tiandiShow
			ref="maptiandi"
			v-if="mapShow"
			:mapInitAddress="mapInitAddress"
			@emitAdressMethod="emitAdressMethod"
			@close="mapShow = false"
			:action="type == 'view' ? 'detail' : 'edit'"
		></tiandiShow>
	</el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ElForm, FormRules } from 'element-plus';
import { RequestSave, JobBaseVO, JobEquipmentDTO } from '../api/types';
import { api_editCenter, api_saveCenter } from '../api';
import { watch } from 'vue';
import tiandiShow from '@/components/select/tiandiShow.vue';
import { setObjValue } from '@/utils';
import { api_getArea, api_getDict } from '@/api/dict';
import { Location } from '@element-plus/icons-vue';
interface Props {
	type: string; //新增还是查看还是编辑
	rowData?: JobBaseVO | null;
}
const props = withDefaults(defineProps<Props>(), {
	type: 'add',
});
const dialogVisible = defineModel<boolean>('visible');

//创建一个作业类型的map映射



const title = computed(() => {
	switch (props.type) {
		case 'add':
			return '新增';
		case 'view':
			return '查看';
		case 'edit':
			return '编辑';
		default:
			break;
	}
});
const emits = defineEmits<{
	(ev: 'saveForm'): void;
}>();
const center_service_type = ref<DicDataItemCode[]>([]);
onMounted(() => {
	api_getDict('center_service_type').then((res) => {
		center_service_type.value = res;
	});
	api_getArea().then((res) => {
		areas.value = res;
	});
});
const defaultData: RequestSave = {
	addressDetail: '', //详细地址
	cityCode: '', //地市编码
	contactName: '', //联系人名称
	contactTelephone: '', //联系人手机号
	districtCode: '', //区县编码
	finishTime: '', //需求完成时间
	jobArea: '', //作业面积
	jobEquipments: [
		{
			key: '1',
			count: '',
			equipmentCode: '',
		},
	], //作业设备
	jobInfo: '', //需求详情
	jobType: '', //作业类型
	latitude: '', //维度
	longitude: '', //经度
	provinceCode: '', //省份编码
	remark: '', //备注

	serviceScopeList: [], //服务范围 0,1,2
	address: [], // 省市县编码
};

const formData = ref<RequestSave>(Object.assign({}, defaultData, { subsidyRule: [] }));
const rules: FormRules<RequestSave> = Object.keys(formData.value).reduce((acc, key) => {
	acc[key] = [
		{
			required: true,
			message: '不能为空',
			trigger: 'blur',
		},
	];
	return acc;
}, {} as FormRules);
delete rules.jobInfo;
const formRules = ref<FormRules<RequestSave>>(rules);
const formRef = ref<InstanceType<typeof ElForm>>();
watch(
	() => dialogVisible,
	(newValue, oldValue) => {
		setObjValue(formData.value, {}, defaultData);
		if (!newValue || props.type == 'add') return;
		setObjValue(formData.value, props.rowData!, defaultData);
		formData.value.address = [formData.value.provinceCode, formData.value.cityCode];
		if (formData.value.districtCode) formData.value.address.push(formData.value.districtCode);
		formData.value.serviceScopeList = formData.value.jobType.split(',');
	},
	{ immediate: true, deep: true },
);
function submit() {
	formRef.value?.validate((valid) => {
		if (!valid) return;
		let param = {
			addressDetail: formData.value.addressDetail, //详细地址
			cityCode: formData.value.cityCode, //地市编码
			contactName: formData.value.contactName, //联系人名称
			contactTelephone: formData.value.contactTelephone, //联系人手机号
			districtCode: formData.value.districtCode, //区县编码
			finishTime: formData.value.finishTime, //需求完成时间
			jobArea: formData.value.jobArea, //作业面积
			jobEquipments: formData.value.jobEquipments.map((d) => {
				return { count: d.count, equipmentCode: d.equipmentCode };
			}), //作业设备
			jobInfo: formData.value.jobInfo, //需求详情
			latitude: formData.value.latitude, //维度
			longitude: formData.value.longitude, //经度
			provinceCode: formData.value.provinceCode, //省份编码
			remark: formData.value.remark, //备注

			jobType: formData.value.serviceScopeList?.toString(), //服务范围 0,1,2
		};
		let api_ = props.type == 'add' ? api_saveCenter : api_editCenter;
		if (props.type == 'edit') param['uuid'] = props.rowData!.uuid;
		api_(param).then((res) => {
			emits('saveForm');
			dialogVisible.value = false;
		});
	});
}

function closeDialog() {
	setObjValue(formData.value, {}, defaultData);
	dialogVisible.value = false;
	formRef.value!.clearValidate();
}

defineExpose({
	open,
	close,
});

const handleChange = (value) => {
	console.log(value);
	formData.value.provinceCode = value[0];
	formData.value.cityCode = value[1];
	if (value[2]) formData.value.districtCode = value[2];
};

const areas = ref<AreaTreeBase[]>([]);

function chooseAddress() {
	mapShow.value = true;
}
const mapShow = ref<boolean>(false);
const mapInitAddress = ref({
	name: '',
	lng: '',
	lat: '',
});
function emitAdressMethod(e: any) {
	formData.value.latitude = e.lat;
	formData.value.longitude = e.lng;
	formData.value.addressDetail = e.name;

	formRef.value!.clearValidate('addressDetail');

	mapShow.value = false;
}
function addDomain() {
	formData.value.jobEquipments.push({
		key: Date.now(),
		count: '',
		equipmentCode: '',
	});
}
function removeDomain(item: JobEquipmentDTO) {
	const index = formData.value.jobEquipments.indexOf(item);
	if (index !== -1) {
		formData.value.jobEquipments.splice(index, 1);
	}
}
function handleTimeChange(date) {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
	const day = String(date.getDate()).padStart(2, '0');
	formData.value.finishTime = `${year}-${month}-${day}`;
	console.log(date, formData.value.finishTime);
}
</script>
<style lang="scss" scoped>
.i-form-item {
	.dialogImg {
		vertical-align: middle;
		margin-top: 3px;
		width: 32px;
		height: 32px;
		cursor: pointer;
	}
}
</style>
