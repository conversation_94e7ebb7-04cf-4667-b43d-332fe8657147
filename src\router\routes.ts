// 不需要权限就可以访问的路由
import { isDev } from '@/config';
import { RouteRecordRaw, RouterView } from 'vue-router';

export const commonRoutes: RouteRecordRaw[] = [
	{
		path: '/index',
		name: 'index',
		alias: '/index.html', // 别名
		// component: () => import('@/views/index/index.vue'),
		redirect: { path: '/main' },
		children: [],
		meta: {
			title: '首页',
			pass: true, // 不需要登录就能进入 默认为 false
		},
	},
	{
		path: '/login',
		name: 'login',
		component: () => import('@/views/login/index.vue'),
		meta: {
			title: '登录',
			pass: true, // 不需要登录就能进入 默认为 false
		},
		children: [],
	},
	{
		path: '/main',
		name: 'main',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '管理',
		},
		children: [],
	},

	{
		path: '/404',
		name: '404',
		component: () => import('@/views/error/404.vue'),
		meta: {
			title: '404',
			pass: true, // 不需要登录就能进入 默认为 false
		},
	},
	{
		path: '/',
		name: 'root',
		redirect: { path: '/index' },
		meta: {
			title: '首页',
			pass: true, // 不需要登录就能进入 默认为 false
		},
		children: [],
	},
];

// main 的子路由，通过当前登录人的权限筛选后添加到 main 的 children中
export const mainRoutes: RouteRecordRaw[] = [
	/* {
		path: '/test001',
		name: 'test001',
		component: () => import('@/views/test001/index.vue'),
		meta: {
			title: 'test001',
			isDev: true,
			// authCode: ['Permission_DeviceRegister', 'Permission_DeviceRegister122'],
			titleLock: true,
		},
	}, */
	{
		path: '/resourceManagement',
		name: 'resourceManagement',
		component: () => import('@/views/main/index.vue'),
		// component: RouterView,
		meta: {
			title: '资源管理',
			isDev: true,
			authCode: '',
		},
		children: [
			{
				path: 'organizationManagement',
				name: 'resourceManagement_organizationManagement',
				component: RouterView,
				meta: {
					title: '组织管理',
					isDev: true,
					authCode: '',
				},
				children: [
					{
						path: 'serviceCenter',
						name: 'resourceManagement_organizationManagement_serviceCenter',
						component: () => import('@/views/resourceManagement/organizationManagement/serviceCenter/index.vue'),
						meta: {
							title: '服务中心',
							isDev: true,
							authCode: '',
						},
					},
					{
						path: 'serviceTeam',
						name: 'resourceManagement_organizationManagement_serviceTeam',
						component: () => import('@/views/resourceManagement/organizationManagement/serviceTeam/index.vue'),
						meta: {
							title: '服务队',
							isDev: true,
							authCode: '',
						},
					},
				],
			},
			{
				path: 'staffManagement',
				name: 'resourceManagement_staffManagement',
				component: RouterView,
				meta: {
					title: '人员管理',
					isDev: true,
					authCode: '',
				},
				children: [
					{
						path: 'serviceCenter',
						name: 'resourceManagement_staffManagement_serviceCenter',
						component: () => import('@/views/resourceManagement/staffManagement/serviceCenter/index.vue'),
						meta: {
							title: '服务中心',
							isDev: true,
							authCode: '',
						},
					},
					{
						path: 'serviceTeam',
						name: 'resourceManagement_staffManagement_serviceTeam',
						component: () => import('@/views/resourceManagement/staffManagement/serviceTeam/index.vue'),
						meta: {
							title: '服务队',
							isDev: true,
							authCode: '',
						},
					},
				],
			},
			{
				path: 'equipmentManagement',
				name: 'resourceManagement_equipmentManagement',
				component: () => import('@/views/resourceManagement/equipmentManagement/index.vue'),
				meta: {
					title: '机具管理',
					isDev: true,
					authCode: '',
				},
			},
		],
	},
	{
		path: '/operationalRequirement',
		name: 'operationalRequirement',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '作业需求',
			isDev: true,
			authCode: '',
		},
		children: [
			{
				path: 'demandManagement',
				name: 'operationalRequirement_demandManagement',
				component: () => import('@/views/jobRequire/requireManagement/index.vue'),
				meta: {
					title: '需求管理',
					isDev: true,
					authCode: '',
				},
			},
		],
	},
	{
		path: '/weatherWarning',
		name: 'weatherWarning',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '天气预警',
			isDev: true,
			authCode: '',
		},
		children: [
			{
				path: 'meteorologicalWarning',
				name: 'weatherWarning_meteorologicalWarning',
				component: () => import('@/views/weatherWarning/meteorologicalWarning/index.vue'),
				meta: {
					title: '气象预警',
					isDev: true,
					authCode: '',
				},
			},
		],
	},
	{
		path: '/jobStatistic',
		name: 'jobStatistic',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '作业统计',
			isDev: true,
			authCode: '',
		},
		children: [
			{
				path: 'workStatistic',
				name: 'jobStatistic_workStatistic',
				component: () => import('@/views/jobStatistic/jobStatisticMenu/index.vue'),
				meta: {
					title: '作业统计',
					isDev: true,
					authCode: '',
				},
			},
		],
	},
];

// if (!isDev) {
// 	const index = mainRoutes.findIndex((item) => item.name === 'demo');
// 	mainRoutes.splice(index, 1);
// }
