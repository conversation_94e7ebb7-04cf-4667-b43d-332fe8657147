export interface getListRequestType {
	key?: string;
	pageNo: number;
	pageSize: number;
	[property: string]: any;
}

/**
 * PageResultJobStatisticsBaseVO
 */
export interface PageResultJobStatisticsBaseVO {
	list: JobStatisticsBaseVO[];
	total: number;
	[property: string]: any;
}

/**
 * JobStatisticsBaseVO
 */
export interface JobStatisticsBaseVO {
	areaArea?: number; //区域总面积
	finishedArea?: number; //区域完成总面积
	manualWorkArea?: number; //人工完成面积
	mechanicalWorkingArea?: number; //机械完成面积
	type?: number; //0七天 1：一个月 2:本年  3:全量数据
	uuid?: string;
	[property: string]: any;
}

/**
 * SaveJobStatisticsDTO
 */
export interface RequestSave {
	areaArea?: number; //区域总面积
	finishedArea?: number; //区域完成总面积
	manualWorkArea?: number; //人工完成面积
	mechanicalWorkingArea?: number; //机械完成面积
	type?: number; //0七天 1：一个月 2:本年  3:全量数据
	[property: string]: any;
}
