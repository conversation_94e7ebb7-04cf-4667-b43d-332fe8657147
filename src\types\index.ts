import { ElPagination, TableInstance, FormInstance, ButtonInstance } from 'element-plus';
declare global {
	type ButtonType = ButtonInstance['type'];
	type ElTableProps = TableInstance['$props'];
	type ElFormInstance = FormInstance;

	// elment 类型问题，去掉只读属性限制
	interface ElPaginationProps extends Omit<InstanceType<typeof ElPagination>['$props'], 'currentPage' | 'pageSize' | 'total'> {
		currentPage?: number;
		pageSize?: number;
		total?: number;
	}
	interface OptionItem {
		value: string | number;
		label: string;
		// [key: string]: any;
	}
	interface TreeDataItem {
		label: string; //	树节点显示的内容	string|slot	'---'

		value?: string; //	树节点显示的内容	string|slot	'---'
		disabled?: boolean; //是否禁用	boolean	false
		isLeaf?: boolean; //	是否是叶子节点	boolean	false 叶子节点就是有展开框
		children?: TreeDataItem[];
	}

	interface CommonPageListParam {
		pageNumber: number; // 当前页码
		pageSize: number; // 每页多少个
		[key: string]: any;
	}
	interface PageInfo extends CommonPageListParam {
		total: number; //	总条目数
	}
	// 分页列表参数 可以扩展额外参数
	type PageListParam<OtherParam = unknown> = CommonPageListParam & {
		[P in keyof OtherParam]: OtherParam[P];
	};

	// 分页列表接口返回值
	interface PageListInfo<List = unknown> {
		pageNo: number; // 当前页码
		pageSize: number; // 每页多少个
		records: List[];
		totalCount: number;
	}
	// 分页列表接口返回值2
	interface PageListInfo2<List = unknown> {
		rows: List[];
		total: number;
	}
	// 分页列表接口返回值3
	interface PageListInfo3<List = unknown> {
		list: List[];
		total: number;
	}

	type DialogType = 'add' | 'view' | 'edit';

	interface areaLatLng {
		code: string;
		parentCode: string;
		level: string;
		name: string;
		latitude: string;
		longitude: string;
	}
}
