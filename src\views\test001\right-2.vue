<template>
	<right-box :title="title">
		{{ rowData }}
		<template #foot>
			<el-button type="primary" size="small" @click="onClick2">关闭</el-button>
		</template>
	</right-box>
</template>

<script lang="ts" setup>
import { ref, watch, inject } from 'vue';
defineOptions({ inheritAttrs: false });
interface Props {
	rowData?: any;
	title: string;
	handleType: 'add' | 'view' | 'edit' | string; //
}
const props = withDefaults(defineProps<Props>(), {});
const emit = defineEmits<{}>();

const toggleRight = inject<ToggleContainerRight>('toggleRight')!; // 调用父组件中的方法
function onClick2() {
	toggleRight(false);
}
</script>
<style lang="scss" scoped></style>
