<template>
	<el-dialog
		v-model="dialogVisible"
		width="1000px"
		:title="title"
		:close-on-click-modal="false"
		append-to-body
		align-center
		destroy-on-close
		@closed="closeDialog"
	>
		<el-form
			ref="formRef"
			style="margin: 0 12px"
			:model="formData"
			:disabled="type == 'view'"
			label-width="135px"
			:rules="formRules"
		>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="所属中心" prop="centerParentUuid">
						<el-select v-model="formData.centerParentUuid" placeholder="请选择所属服务中心">
							<el-option
								v-for="item in centerListOptions"
								:key="item.centerUuid"
								:label="item.centerName"
								:value="item.centerUuid"
							/>
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12">
					<el-form-item label="联系人" prop="contactName">
						<el-input v-model="formData.contactName" placeholder="请输入联系人名称"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="队伍名称" prop="name">
						<el-input v-model="formData.name" placeholder="请输入服务队名称"></el-input>
					</el-form-item>
				</el-col>

				<el-col :span="12">
					<el-form-item label="联系方式" prop="contactTelephone">
						<el-input v-model="formData.contactTelephone" placeholder="请输入联系方式" 	:disabled="type === 'edit'"></el-input>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row :gutter="24">
				<el-col :span="12">
					<el-form-item label="统一社会信用代码" prop="creditCode">
						<el-input v-model="formData.creditCode" placeholder="请输入统一社会信用代码"></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="24">
					<el-form-item label="服务类型" prop="serviceScopeList">
						<el-select v-model="formData.serviceScopeList" multiple placeholder="请选择服务类型">
							<el-option v-for="item in center_service_type" :key="item.code" :label="item.name" :value="item.code" />
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col :span="14">
					<el-form-item label="所在位置" prop="address">
						<el-cascader
							:props="{
								value: 'areaId',
								label: 'areaName',
								children: 'children',
							}"
							placeholder="请选择行政区划"
							style="width: 100%"
							v-model="formData.address"
							:options="areas"
							@change="handleChange"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="10">
					<el-form-item style="position: relative; margin-left: -135px" label="" prop="addressDetail">
						<el-input @click.native="chooseAddress" v-model="formData.addressDetail" placeholder="请选择详细地址">
							<template #suffix>
								<el-icon @click.native="chooseAddress" class="el-input__icon"><Location /></el-icon> </template
						></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="24">
				<el-col>
					<el-form-item label="备注" prop="remark">
						<el-input v-model="formData.remark" placeholder="请输入备注信息" type="textarea" :rows="3" resize="none" />
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<el-row justify="center">
			<el-button @click="closeDialog" style="margin-right: 12px">取消</el-button>
			<el-button type="primary" @click="submit" v-if="type !== 'view'" style="margin-right: 12px">确认</el-button>
		</el-row>
		<!-- 地图弹窗 -->
		<tiandiShow
			ref="maptiandi"
			v-if="mapShow"
			:mapInitAddress="mapInitAddress"
			@emitAdressMethod="emitAdressMethod"
			@close="mapShow = false"
			:action="type == 'view' ? 'detail' : 'edit'"
		></tiandiShow>
	</el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ElForm, FormRules } from 'element-plus';
import { Location } from '@element-plus/icons-vue';
import { CenterList, RequestSaveCenter, ServiceCenterBaseVO } from '../api/types';
import tiandiShow from '@/components/select/tiandiShow.vue';
import { api_editCenter, api_getCenterList, api_saveCenter } from '../api';
import { watch } from 'vue';
import { setObjValue } from '@/utils';
import { api_getArea, api_getDict } from '@/api/dict';
import { areasLatLng } from '@/assets/areaLatLng';
import axios from 'axios';
import { storeUserInfo } from '@/store';
interface Props {
	type: string; //新增还是查看还是编辑
	rowData?: ServiceCenterBaseVO | null;
}
const props = withDefaults(defineProps<Props>(), {
	type: 'add',
});
const dialogVisible = defineModel<boolean>('visible');

const title = computed(() => {
	switch (props.type) {
		case 'add':
			return '新增';
		case 'view':
			return '查看';
		case 'edit':
			return '编辑';
		default:
			break;
	}
});
const emits = defineEmits<{
	(ev: 'saveForm'): void;
}>();

const centerListOptions = ref<CenterList[]>([]);
onMounted(() => {
	api_getDict('center_service_type').then((res) => {
		center_service_type.value = res;
	});
	api_getArea().then((res) => {
		console.log(res, '8888');
		areas.value = res;
	});
	api_getCenterList({
		tenantId: storeUserInfo.enterpriseId,
	}).then((res) => {
		centerListOptions.value = res;
	});
});
const defaultData: RequestSaveCenter = {
	addressDetail: '', //详细地址
	centerParentUuid: '', //服务中心uuid
	cityCode: '', //地市编码
	contactName: '', //联系人名称
	contactTelephone: '', //联系人手机号
	creditCode: '', //统一信用编码
	districtCode: '', //区县编码
	latitude: '', //维度
	longitude: '', //经度
	name: '', //区域服务中心名称
	provinceCode: '', //省份编码
	remark: '', //备注
	serviceScope: '', // 服务范围 0,1,2
	serviceScopeList: [], //服务范围 0,1,2
	address: [], // 省市县编码
};

const formData = ref<RequestSaveCenter>(Object.assign({}, defaultData, { subsidyRule: [] }));
const rules: FormRules<RequestSaveCenter> = Object.keys(formData.value).reduce((acc, key) => {
	acc[key] = [
		{
			required: true,
			message: '不能为空',
			trigger: 'blur',
		},
	];
	return acc;
}, {} as FormRules);

const formRules = ref<FormRules<RequestSaveCenter>>(rules);
delete formRules.value.creditCode;
delete formRules.value.remark;
const formRef = ref<InstanceType<typeof ElForm>>();
watch(
	() => dialogVisible,
	(newValue, oldValue) => {
		setObjValue(formData.value, {}, defaultData);
		if (!newValue || props.type == 'add') return;
		setObjValue(formData.value, props.rowData!, defaultData);
		formData.value.address = [formData.value.provinceCode, formData.value.cityCode];
		if (formData.value.districtCode) formData.value.address.push(formData.value.districtCode);
		formData.value.serviceScopeList = formData.value.serviceScope ? formData.value.serviceScope.split(',') : '';
		if (formData.value.addressDetail) mapInitAddress.value.name = formData.value.addressDetail;
		mapInitAddress.value.lat = formData.value.latitude!;
		mapInitAddress.value.lng = formData.value.longitude!;
	},
	{ immediate: true, deep: true },
);
function submit() {
	formRef.value?.validate((valid) => {
		if (!valid) return;
		let param = {
			addressDetail: formData.value.addressDetail, //详细地址
			centerParentUuid: formData.value.centerParentUuid, //服务中心uuid
			cityCode: formData.value.cityCode, //地市编码
			contactName: formData.value.contactName, //联系人名称
			contactTelephone: formData.value.contactTelephone, //联系人手机号
			creditCode: formData.value.creditCode, //统一信用编码
			districtCode: formData.value.districtCode, //区县编码
			latitude: formData.value.latitude, //维度
			longitude: formData.value.longitude, //经度
			name: formData.value.name, //区域服务中心名称
			provinceCode: formData.value.provinceCode, //省份编码
			remark: formData.value.remark, //备注
			serviceScope: formData.value.serviceScopeList?.toString(), //服务范围 0,1,2
		};
		let api_ = props.type == 'add' ? api_saveCenter : api_editCenter;
		if (props.type == 'edit') {
			param['uuid'] = props.rowData!.uuid;
			param['tenantId'] = props.rowData!.tenantId;
		}
		api_(param).then((res) => {
			emits('saveForm');
			dialogVisible.value = false;
		});
	});
}

function closeDialog() {
	setObjValue(formData.value, {}, defaultData);
	dialogVisible.value = false;
	formRef.value!.clearValidate();
}

defineExpose({
	open,
	close,
});

const handleChange = (value) => {
	console.log(value);
	formData.value.provinceCode = value[0];
	formData.value.cityCode = value[1];
	if (value[2]) formData.value.districtCode = value[2];
};

function chooseAddress() {
	mapShow.value = true;
}
const mapShow = ref<boolean>(false);
const mapInitAddress = ref({
	name: '',
	lng: '',
	lat: '',
});
function emitAdressMethod(e: any) {
	formData.value.addressDetail = e.name;
	formData.value.latitude = e.lat;
	formData.value.longitude = e.lng;
	formRef.value!.clearValidate('addressDetail');
	mapShow.value = false;
}
const center_service_type = ref<DicDataItemCode[]>([]);
const areas = ref<AreaTreeBase[]>([]);
</script>
<style lang="scss" scoped>
.add-btn-t {
	width: 100%;
	padding: 12px 0;
	display: flex;
	justify-content: center;
	border-radius: 12px;
	background-color: #ecf5ff;
	margin-bottom: 12px;
	// border: 1px solid #f2f3f5;

	cursor: pointer;
	// color: #ecf5ff;
	// border: 1px solid #ecf5ff;
}
</style>
