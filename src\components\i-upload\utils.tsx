import { isArray } from '@/utils/is';
import { ElMessage, ElMessageBox } from 'element-plus';

export function showResultTips(successTips: boolean, errorTips: boolean, success: Array<ResponseUpload>, errors: UploadErrorFile[]) {
	console.log('11111111111111111111111');

	if (success.length && errors.length === 0) {
		// 全部成功
		if (successTips) {
			let str = '';
			if (success.length === 1) {
				// 只有一个
				const file = success[0].file;
				str = file.fileName + `.${file.fileType} 上传成功！`;
			} else {
				str = success.length + '个文件全部上传成功！';
			}
			ElMessage.success(str);
		}
	} else if (success.length === 0 && errors.length) {
		// 全部失败
		if (errorTips) {
			ElMessageBox({
				type: 'error',
				title: '上传失败',
				closeOnClickModal: true,
				message: getErrorsVNode(errors),
			});
		}
	} else {
		// 既有成功也有失败
		if (errorTips) {
			const arr: any[] = [];
			if (success.length) {
				arr.push(getSuccessVNode(success));
			}
			if (errors.length) {
				arr.push(getErrorsVNode(errors));
			}
			ElMessageBox({
				type: 'error',
				title: '上传结果',
				closeOnClickModal: true,
				message: <div>{arr}</div>,
			});
		}
	}
}
function getSuccessVNode(success: Array<ResponseUpload>) {
	const arr: any[] = success.map((item, index) => {
		const file = item.file;
		return (
			<div>
				{index + 1}、{file.fileName}.{file.fileType}
			</div>
		);
	});
	return (
		<span>
			<div style="font-weight: bold;color: var(--el-color-primary);">上传成功列表</div>
			{arr}
		</span>
	);
}
function getErrorsVNode(errors: UploadErrorFile[]) {
	const arr: any[] = errors.map((item, index) => {
		return (
			<div>
				{index + 1}、{item.fileName} （{item.message}）
			</div>
		);
	});
	return (
		<span>
			<div style="font-weight: bold;color: var(--el-color-error);">上传失败列表</div>
			{arr}
		</span>
	);
}
export function showValidateTips(errors: string | string[]) {
	let arr: any[] = [];
	if (typeof errors === 'string') {
		arr.push(
			<span>
				<div style="font-weight: bold;color: var(--el-color-error);">上传失败列表</div>
			</span>,
		);
	} else if (isArray(errors)) {
		arr = errors.map((item, index) => {
			return (
				<div>
					{index + 1}、{item}
				</div>
			);
		});
	}
	ElMessageBox({
		type: 'error',
		title: '文件校验失败',
		closeOnClickModal: true,
		message: <div>{arr}</div>,
	});
}

// 组件内部校验
export function beforeUpload_(files: File[], fileSize: number = 0, accept: string | string[] = []) {
	console.log('11111111111111 beforeUpload_', files);
	// 如果没有限制，则直接通过
	if (!fileSize && accept.length === 0) return Promise.resolve();
	return new Promise((resolve, reject) => {
		const maxFizeStr = Math.ceil((fileSize / 1024 / 1024) * 1000) / 1000;
		const acceptStr = typeof accept === 'string' ? accept : accept.join(',');
		const errorList: string[] = []; // 错误的文件
		files.forEach((file) => {
			if (fileSize) {
				if (file.size > fileSize) {
					errorList.push(`${file.name} 大小超过限制（最大${maxFizeStr}M）`);
				}
			}
			if (accept && accept.length) {
				// 如果是原生 accept 如 'image/*' 不好判断，直接通过，让用户在自定义 校验方法中判断
				if (typeof accept !== 'string') {
					// 使用 file.type 判断不准确，如  xls格式的 type可能是 "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
					// 所以改为 文件名判断
					const fileName = file.name;
					const suffixArr = fileName.split('.');
					let suffix = suffixArr[suffixArr.length - 1];
					suffix = suffix.toLocaleLowerCase(); // 通过小写判断
					const has = accept.some((acc) => acc === suffix);
					if (!has) {
						errorList.push(`${file.name} 格式错误（格式限制：${acceptStr}）`);
					}
				}
			}
		});
		if (errorList.length) {
			reject(errorList);
		} else {
			resolve(true);
		}
	});
}
