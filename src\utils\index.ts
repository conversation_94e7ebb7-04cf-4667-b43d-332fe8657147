import { envConfig, isDev, root<PERSON><PERSON>in, rootUri } from '@/config';
import { cloneDeep } from 'lodash-es';
import { isArray, isObject } from './is';
import { ElMessage, ElMessageBox } from 'element-plus';
import { storeAuthorities } from '@/store';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
/**
 * 获取标题
 */
export function getPageTitle(pageTitle?: string) {
	// return pageTitle ? `${envConfig.documentTitle} - ${pageTitle}` : envConfig.documentTitle;
	return pageTitle ? `${envConfig.documentTitle} ` : envConfig.documentTitle;
}
/**
 * 设置url地址中的参数
 */
export function setUrlParam(data: any, encode: boolean = true): string {
	data = data || {};
	const param: string[] = [];
	for (let key in data) {
		if (data[key] !== undefined && data[key] !== null && data[key] !== '') {
			if (encode) {
				param.push(key + '=' + encodeURIComponent(data[key]));
			} else {
				param.push(key + '=' + data[key]);
			}
		}
	}
	if (param.length) {
		return '?' + param.join('&');
	} else {
		return '';
	}
}

/**
 * 比较两个变量是否相等 当相等的时候 返回 true
 * @param oldVal 旧值
 * @param newVal 新值
 */
export const diff = (oldVal: any, newVal: any): boolean => {
	if (isObject(oldVal)) {
		if (!isObject(newVal)) return false;
		if (Object.keys(oldVal).length !== Object.keys(newVal).length) return false;
		return Object.keys(oldVal).every((key) => {
			return diff(oldVal[key], newVal[key]);
		});
	} else if (isArray(oldVal)) {
		if (!isArray(newVal)) return false;
		if (oldVal.length !== newVal.length) return false;
		return oldVal.every((item, index) => {
			return diff(item, newVal[index]);
		});
	} else {
		return oldVal === newVal;
	}
};
/**
 * 给对象赋值   只接受第一个值的参数 只能赋值第一层，不支持深度赋值
 * 相比 Object.assign 不会增加额外的属性
 * @param obj
 * @param newObj
 */
export function setObjValue<T = Record<string, any>>(obj: T, newObj: Partial<T>, defaultObj?: T) {
	newObj = newObj ?? ({} as T);
	if (defaultObj) {
		Object.keys(obj as object).forEach((key) => {
			if (key == 'assessmentReportFile') {
				obj['assessmentReportFiles'] = [newObj[key]];
			}
			obj[key] = newObj[key] ?? cloneDeep(defaultObj[key]);
		});
	} else {
		Object.keys(obj as object).forEach((key) => {
			obj[key] = newObj[key];
		});
	}
}
// 如果新值没有的话，就继续使用旧值
export function setObjValue2<T = Record<string, any>>(obj: T, newObj: Partial<T>) {
	newObj = newObj ?? ({} as T);
	Object.keys(obj as object).forEach((key) => {
		obj[key] = newObj[key] ?? obj[key];
	});
}

//新打开一个路由页面
export function openNewWindow(url: string, param?: Record<string, any>) {
	window.open(`${rootOrigin}${url}${setUrlParam(param)}`);
}
//跳转构力三方
export function openNewWindowUri(url: string, param: Record<string, any>) {
	window.open(`${rootUri}${url}${setUrlParam(param)}`);
}

export function getTreeDataByKey(tree, key) {
	let res: any = [];
	const recuFun = (arr) => {
		if (!Array.isArray(arr)) return;
		if (!arr.length) return;
		let checked = arr.filter((c) => c[key]);
		res = [...res, ...checked];
		arr.forEach((ch) => recuFun(ch.childList));
	};
	recuFun(tree);
	return res;
}

/**
 * 前台本地预览用户上传的文件
 * @param file  用户上传的文件对象
 */
export function getFileURLByFile(file: File): string {
	try {
		let url = '';
		if (window.webkitURL) {
			url = window.webkitURL.createObjectURL(file);
		} else {
			url = window.URL.createObjectURL(file);
		}

		return url;
	} catch (error) {
		ElMessage.error('文件对象不存在，请刷新重试！');
		return '';
	}
}
//根据文件大小选择合适的单位
export function getFileSize(value: number): string {
	if (!value) return '';
	const units = ['B', 'KB', 'MB', 'GB', 'TB'];
	for (let i = 0; i < units.length; i++) {
		let rel = value / Math.pow(1024, i);
		if (rel < 1024) {
			return Math.round(rel * 100) / 100 + ' ' + units[i]; // 如果是MB以上的单位，则取小数点后两位
		}
	}
	return value + ' ' + units[0];
}
export function commonConfirm(message: string, title = '提示') {
	return ElMessageBox({
		type: 'warning',
		title: title,
		message: message,
		showCancelButton: true,
		showClose: false,
	});
}

// 保存静态资源到本地
export function downloadPublicFile(fileName: string) {
	let url = location.pathname;
	const BASE_URL = rootOrigin;
	if (url.includes('/index.html')) {
		// 说明是 Hash 路由
		url = url.replace('index.html', fileName);
	} else {
		if (BASE_URL === '/') {
			url = '/' + fileName;
		} else {
			url = BASE_URL + '/' + fileName;
		}
	}
	const a = document.createElement('a');
	a.download = fileName;
	a.href = url;
	a.click();
}
/**
 * 获取权限
 */
export function getAuth(auth?: string | string[]): boolean {
	if (!auth) return true; // 没有时，默认true
	if (typeof auth === 'string') return storeAuthorities.value.includes(auth);
	return auth?.some((key) => storeAuthorities.value.includes(key));
}

interface LineListItem {
	parentId: string;
	uuid: string;
	[key: string]: any;
}
interface TreeListItem {
	children: TreeListItem[];
	parentId: string;
	uuid: string;
	[key: string]: any;
}
export function lineListToTree(list: LineListItem[] = []): TreeListItem[] {
	const stairIds: string[] = []; // 顶级的id，默认展开用
	// 先将所有id取出来，用来判断是否有 父级id
	const uuids = list.map((item) => item.uuid);
	const group: Record<string, TreeListItem[]> = {};
	const rootTree: TreeListItem[] = [];
	list.forEach((item) => {
		const { parentId, uuid } = item;
		const newItem: TreeListItem = {
			...item,
			children: [],
		};
		// 所有的item都有 parentId 通过 parentId 去 uuids里找，如果没有找到，说明自己没有父亲，是顶级
		if (parentId && !uuids.includes(parentId)) {
			stairIds.push(uuid);
			rootTree.push(newItem);
		} else {
			group[parentId] = group[parentId] || [];
			group[parentId].push(newItem);
		}
	});
	const queue: TreeListItem[] = [...rootTree];
	while (queue.length) {
		const node: TreeListItem = queue.shift()!;
		const uuid = node.uuid;
		const children = group[uuid] && group[uuid].length ? group[uuid] : null;
		if (children) {
			node.children = children;
			queue.push(...children);
		}
	}
	return rootTree;
}
export function getFileUrl(file?: ResponseFileExtend | null): string {
	if (!file) return '';
	return envConfig.VITE_BASE_FILE_SATIC_PATH + file.fileAccessPath;
}
interface RegionObj {
	regionNameProvince: string;
	regionNameCity: string;
	regionName: string;
	// [key: string]: any;
}
export function getRegionFullName(obj?: RegionObj | null): string {
	if (!obj) return '';
	let fullName: string[] = [];
	obj.regionNameProvince && fullName.push(obj.regionNameProvince);
	obj.regionNameCity && fullName.push(obj.regionNameCity);
	obj.regionName && fullName.push(obj.regionName);
	return fullName.join('/');
}

//导出表格
export function exportToExcelWithCustomHeaders(data: any, headers: any, fileName = 'export.xlsx', options = {}) {
	// 转换数据以使用自定义表头
	const exportData = data.map((item) => {
		const newItem = {};
		Object.keys(headers).forEach((key) => {
			newItem[headers[key]] = item[key];
		});
		return newItem;
	});

	// 其余代码与之前相同
	const workbook = XLSX.utils.book_new();
	const worksheet = XLSX.utils.json_to_sheet(exportData);
	// 获取工作表范围
	const range = XLSX.utils.decode_range(worksheet['!ref']!);

	// 为所有单元格设置自动换行
	for (let R = range.s.r; R <= range.e.r; ++R) {
		for (let C = range.s.c; C <= range.e.c; ++C) {
			const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
			if (!worksheet[cellAddress]) continue;

			worksheet[cellAddress].s = worksheet[cellAddress].s || {};
			worksheet[cellAddress].s.alignment = worksheet[cellAddress].s.alignment || {};
			worksheet[cellAddress].s.alignment.wrapText = true;
		}
	}
	// 设置列宽
	if (options.columnWidths) {
		worksheet['!cols'] = options.columnWidths.map((width) => ({ width }));
	}
	XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
	const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
	const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
	saveAs(blob, fileName);
}

interface RegionNode {
	areaId: string;
	areaName: string;
	parentAreaId: string;
	children: RegionNode[];
}

//根据省市县code，拼接出省市县地名
export function getRegionFullNameTS(regionTree: RegionNode[], provinceCode: string, cityCode?: string, countyCode?: string): string {
	// 实现逻辑同上
	// 处理空数据情况
	if (!regionTree || !regionTree.length) return '未知地区';

	try {
		let result = '';
		const province = regionTree.find((item) => item.areaId === provinceCode);
		if (!province) return '未知地区';
		result += province.areaName;

		// 检查是否有市级数据
		if (!cityCode || !province.children) return result;

		const city = province.children.find((item) => item.areaId === cityCode);
		if (!city) return result;
		result += city.areaName;

		// 检查是否有县级数据
		if (!countyCode || !city.children) return result;

		const county = city.children.find((item) => item.areaId === countyCode);
		if (!county) return result;
		result += county.areaName;

		return result;
	} catch (error) {
		console.error('处理行政区划数据时出错:', error);
		return '未知地区';
	}
}
