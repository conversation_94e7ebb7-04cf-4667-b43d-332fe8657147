<template>
	<common-table
		:columns="columns"
		ref="refTable"
		:getList="getList"
		:total="pageTotal"
		:data="tableData"
		:getBtns="btns"
		:operate-width="200"
	>
		<template #header>
			<el-button @click="resetDateFilter">重置date列筛选</el-button>
			<el-button @click="clearFilter">重置所有列筛选</el-button>
			<el-button @click="clearSort">重置排序</el-button>
			<el-button @click="clearSearchDate">重置name列搜索</el-button>
			<el-button @click="clearSearch">重置所有列搜索</el-button>
			<div style="flex-grow: 1"></div>
			<el-button type="primary" @click="addItem">新增</el-button>
		</template>
	</common-table>
</template>

<script lang="ts" setup>
import { ref, watch, inject, reactive } from 'vue';
import { TableColumnCtx } from 'element-plus';
import { ResponseInfo, api_getList } from './api';

const toggleRight = inject<ToggleContainerRight>('toggleRight')!; //   调用父组件中的方法
const onRightClose = inject<Function>('onRightClose')!; //   调用父组件中的方法
// 注册右侧关闭事件,如果关闭时传入了refresh字段,则说明需要刷新列表
onRightClose((data) => {
	console.log('11111111111111111 onRightClose', data);
	if (data?.refresh) {
		refTable.value?.refreshList();
	}
});

const refTable = ref<CommonTableInstance>();
const columns: CommonTableColumn<ResponseInfo>[] = [
	{
		label: '日期',
		prop: 'date',
		width: 180,
		sortable: 'custom',
		columnKey: 'date', // 筛选时需要这个key
		filterMethod: filterHandler,
		// filteredValue: ['2016-05-01'],
		filters: [
			{ text: '2016-05-01', value: '2016-05-01' },
			{ text: '2016-05-02', value: '2016-05-02' },
			{ text: '2016-05-03', value: '2016-05-03' },
			{ text: '2016-05-04', value: '2016-05-04' },
		],
	},
	{
		label: '名字',
		width: 180,
		prop: 'name',
		searchable: 'custom',
		sortable: 'custom',
	},
	{
		label: '地址',
		prop: 'address',
		searchable: 'custom',
	},
	{
		label: '标签',
		prop: 'tag',
		columnKey: 'tag', // 筛选时需要这个key
		width: 100,
		sortable: 'custom', // true,
		filterMethod: filterTag,
		filters: [
			{ text: 'Home', value: 'Home' },
			{ text: 'Office', value: 'Office' },
		],
	},
	{
		label: '状态',
		fixed: 'right',
		prop: 'state',
		states: [
			{ value: 10, label: '启用', color: 'blue' },
			{ value: 20, label: '禁用', color: 'var(--el-color-info-light-3)' },
		],
		width: 80,
	},
];
const btns: OptionBtn<ResponseInfo>[] = [
	{
		label: '查看1',
		auth: '',
		onClick(row) {
			console.log('点击了按钮', row);
			toggleRight(true, {
				title: '测试标题11111',
				data: {
					type: 'right1',
					handleType: 'view',
					rowData: row,
				},
			});
		},
	},
	{
		label: '查看2',
		auth: '',
		onClick(row) {
			console.log('点击了按钮', row);
			toggleRight(true, {
				title: '测试标题22222',
				data: {
					type: 'right2',
					handleType: 'view',
					rowData: row,
				},
			});
		},
	},
	{
		label: '隐藏的按钮1',
		auth: '',
		hide: true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '隐藏的按钮2',
		auth: '',
		hide: () => true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '禁用按钮',
		auth: '',
		disabled: () => true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '删除',
		auth: '',
		tips: '确定删除吗？',
		placement: 'left-start',
		more: true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '编辑',
		auth: '',
		onClick(row) {
			console.log('点击了按钮', row);
			toggleRight(true, {
				isEdit: true, // 当前抽屉是否处于编辑状态，如果是，则下次打开新抽屉时，需要确认提示
				title: '测试标题11111',
				data: {
					type: 'right1',
					handleType: 'edit',
					rowData: row,
				},
			});
		},
	},
	{
		label: '禁用按钮',
		auth: '',
		disabled: () => true,
		more: true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
];
const pageTotal = ref(0);
const tableData = ref<ResponseInfo[]>([]);
function getList(param?: CommonTableParams) {
	console.log('1111111111 调用了getList param:', param);
	const param_: CommonTableParams = param || {
		pageNumber: 1,
		pageSize: 20,
	};
	return api_getList(param_).then((list) => {
		tableData.value = list;
		pageTotal.value = 200;
	});
}
getList();
function resetDateFilter() {
	refTable.value?.handle('clearFilter', ['date']);
}
function clearFilter() {
	refTable.value?.handle('clearFilter');
}
function filterTag(value: string, row: ResponseInfo) {
	// return row.tag === value;
	return true;
}
function filterHandler(value: string, row: ResponseInfo, column: TableColumnCtx<ResponseInfo>) {
	const property = column['property'];
	// return row[property] === value;
	return true;
}
function clearSort() {
	refTable.value?.clearSort();
}
function clearSearchDate() {
	refTable.value?.clearSearch(['name']);
}
function clearSearch() {
	refTable.value?.clearSearch();
}
function addItem() {
	toggleRight(true, {
		isEdit: true, // 当前抽屉是否处于编辑状态，如果是，则下次打开新抽屉时，需要确认提示
		title: '新增数据',
		data: {
			type: 'right1',
			handleType: 'add',
		},
	});
}
</script>
<style lang="scss" scoped></style>
