import { createApp } from 'vue';
import { createPinia } from 'pinia';
import axios from 'axios';
import http from '@/utils/axios';
import App from './App.vue';
import { initRouter } from '@/router';
import moment from 'moment';

import { envConfig, isDev, rootOrigin } from '@/config';
import { reSize } from '@/store';
// import { imports } from '@/utils/imports';

import 'element-plus/es/components/message/style/css';
import 'element-plus/es/components/message-box/style/css';

// import 'dayjs/locale/zh-cn';
axios.get(`${rootOrigin}/config.json?v_=${Math.random()}`).then(async (info) => {
	Object.assign(envConfig, info?.data || {});
	// if (appName) {
	// 	envConfig.VITE_APP_NAME = appName; // 使用前台的 appName
	// }
	// ['VITE_BASE_API'].forEach((key) => {
	// 	if (envConfig[key]?.indexOf('http') < 0) {
	// 		// 如果不是http开头，说明是相对路径，要补全路径
	// 		envConfig[key] = location.origin + envConfig[key];
	// 	}
	// });

	console.log('11111111111111111 envConfig', envConfig);
	const app = createApp(App);
	let routerInstance: any = null;
	routerInstance = initRouter();

	app.use(routerInstance);
	app.use(createPinia());
	// if (isDev) {
	// 	imports(app);
	// }
	reSize();
	app.config.globalProperties.$moment = moment; // 给地图组件使用，兼容旧代码
	http.setOriginHref(isDev ? '' : envConfig.VITE_BASE_API);
	routerInstance.isReady().then(() => app.mount('#app'));
	setTimeout(function () {
		G_loading.value = false;
	}, 0);
});
