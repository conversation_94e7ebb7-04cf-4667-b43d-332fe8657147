<template>
	<el-form-item v-bind="$attrs" class="i-form-item" ref="refDom">
		<i-upload
			v-bind="uploadProps || {}"
			:multiple="false"
			:accept="accept"
			:disabled="disabled"
			:fileSize="fileSize"
			class="t-upload-box"
			:style="{
				width: imgWidth,
				height: imgHeight,
			}"
			@change="onChange"
		>
			<template v-if="modelValue">
				<div class="t-close" title="移除" v-if="!disabled" @click.stop="onDeletImage">
					<el-icon><Close /></el-icon>
				</div>
				<el-image @click.stop="" :src="modelValue" :preview-src-list="[modelValue]" preview-teleported fit="cover" />
			</template>
			<template v-else>
				<!-- <slot><div class="t-upload-text">上传</div> </slot> -->
				<slot> </slot>
			</template>
		</i-upload>
		<div class="t-other-text">
			<slot name="other"> </slot>
		</div>
	</el-form-item>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { Close } from '@element-plus/icons-vue';
import { envConfig } from '@/config';
import { api_getImageInfo } from '@/api';
interface Props {
	modelValue?: string;
	fileObj?: ResponseFile | null;
	imgWidth?: string;
	imgHeight?: string;
	rules?: [Object, Array<any>, Boolean];
	required?: boolean;
	disabled?: boolean;
	placeholder?: string;
	accept?: string[] | string; // 文件格式 string 格式是原生格式， string[]是自定义格式，需要转成原生格式
	fileSize?: number; // 文件限制大小 单位 M
	uploadProps?: UploadProps;
	extend: TypeExtend;
}
const props = withDefaults(defineProps<Props>(), {
	placeholder: '请上传',
	accept: () => ['png', 'jpg', 'jpeg'],
	fileSize: 5,
	imgWidth: '150px',
	imgHeight: '100px',
	required: true,
});

const emit = defineEmits<{
	(ev: 'update:modelValue', value: string): void;
	(ev: 'update:fileObj', fileObj: ResponseFile | null): void; // 每次失去焦点时，都会触发这个事件
	(ev: 'change', fileObj: ResponseFile | null): void; // 每次失去焦点时，都会触发这个事件
	(ev: 'cardInfo', infoObj: any): void; // 每次失去焦点时，都会触发这个事件
}>();
const refDom = ref();

const modelValue_ = computed({
	get() {
		return props.modelValue;
	},
	set(value) {
		emit('update:modelValue', value!);
	},
});

const fileObj_img = ref<ResponseFileExtend | null>(null); //   文件对象
watch(
	() => props.fileObj,
	(val) => {
		setFileObj(val);
	},
	{
		immediate: true,
		deep: true,
	},
);

function onChange(ev: EventUploadChange) {
	if (!ev.successFileList.length) return;
	const file = ev.successFileList[0].file || null;
	setFileObj(file);

	if (props.extend === 'idCardPortrait' || props.extend === 'license') {
		api_getImageInfo({
			fileUrl: file.fileStoragePath,
			ocrType: props.extend === 'idCardPortrait' ? 'idcard' : 'blicense',
			urlType: 'relative',
		}).then((res) => {
			emit('cardInfo', res);
		});
	}
	emit('update:fileObj', fileObj_img.value);
	emit('change', fileObj_img.value);
}
function onDeletImage() {
	setFileObj(null);
	emit('update:fileObj', null);
	emit('change', null);
}
function setFileObj(file?: ResponseFileExtend | null) {
	if (file) {
		fileObj_img.value = {
			...file,
			extend: props.extend,
		};
		modelValue_.value = envConfig.VITE_BASE_FILE_SATIC_PATH + fileObj_img.value.fileAccessPath;
	} else {
		modelValue_.value = '';
		fileObj_img.value = null;
	}
	refDom.value?.validate('change');
}
</script>
<style lang="scss" scoped>
.t-upload-box {
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	flex-direction: column;
	//border: 1px dashed var(--el-border-color);

	.t-close {
		position: absolute;
		top: 3px;
		right: 3px;
		z-index: 3;
		border: 1px solid #136C4D;
		border-radius: 50%;
		width: 14px;
		height: 14px;
		:deep(.el-icon) {
			position: relative;
			top: -7px;
		}
	}
	.el-image {
		width: 100%;
		height: 100%;
	}
}
.t-upload-text {
	line-height: 1;
}
.t-other-text {
	width: 100%;
	color: var(--el-color-info-light-3);
}
.i-form-item {
	&.is-error {
		:deep(.t-upload-box) {
			border: 1px solid transparent;
			box-shadow: 0 0 0 1px var(--el-color-danger) inset;

			// border-color: val(--el-color-error);
		}
	}
}
</style>
