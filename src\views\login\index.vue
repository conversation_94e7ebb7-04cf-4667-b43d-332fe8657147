<template>
	<div>登录页--正在开发。。。</div>
	<el-form label-width="100px" :model="formState" style="max-width: 460px">
		<el-form-item label="用户名">
			<el-input v-model="formState.loginName" />
		</el-form-item>
		<el-form-item label="密码">
			<el-input v-model="formState.passWord" />
		</el-form-item>
	</el-form>
	<el-button type="primary" @click="onClick">登录</el-button>
	<el-button type="primary" @click="onClick2">后台</el-button>
	<el-button type="primary" @click="onClick3">首页</el-button>
</template>

<script lang="ts" setup>
import { reactive, onMounted } from 'vue';
import { routerInstance } from '@/router';
import { api_login, RequestLogin } from './api';
import { setTokenLocal } from '@/utils/token';

const formState = reactive<RequestLogin>({
	loginName: '',
	passWord: '',
});

function onClick() {
	// const passWord = md5(formState.passWord);
	// console.log('1111111111111111111 passWord', passWord);
	api_login(formState).then((info) => {
		setTokenLocal(info.token);
		routerInstance.push({
			name: 'main',
		});
	});
}
function onClick2() {
	routerInstance.push({
		name: 'main',
	});
}
function onClick3() {
	routerInstance.push({
		name: 'index',
	});
}
</script>
<style lang="scss" scoped></style>
