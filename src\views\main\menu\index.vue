<!-- unique-opened -->
<template>
	<el-menu
		class="t-menu"
		:collapse="collapsed"
		:default-active="(currentRoute.name as string)"
		@select="onSelect"
		@open="handleOpen"
		@close="handleClose"
	>
		<template v-for="item in (storeSelectFirstMenu?.children as unknown as RouteRecordRaw[])">
			<MenuItem isFirst :itemObj="item" />
		</template>
	</el-menu>
</template>

<script lang="tsx" setup>
import { RouteRecordRaw, useRoute } from 'vue-router';
import { routerInstance } from '@/router';
import MenuItem from './menu-item.vue';
import { PropType, ref, watch } from 'vue';
import { setStoreCommon, storeCommon, storeMenus, storeSelectFirstMenu } from '@/store';
interface Props {
	collapsed?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
	collapsed: false,
});
const currentRoute = useRoute(); // 当前路由
const onSelect = (key: string, keyPath: string[]) => {
	console.log('12222222222222', key, keyPath, storeSelectFirstMenu, storeCommon, storeMenus, currentRoute);

	if (currentRoute.name === key) {
		// 路由重复，调用刷新路由方法
		setStoreCommon({
			refresh: storeCommon.refresh + 1,
		});
		return;
	}
	routerInstance.push({
		name: key,
	});
};
const handleOpen = (key: string, keyPath: string[]) => {
	console.log(key, keyPath);
};
const handleClose = (key: string, keyPath: string[]) => {
	console.log(key, keyPath);
};
</script>
<style lang="scss" scoped>
.t-menu {
	border-right: none;
	padding-top: 10px;
	:deep(.el-menu-item) {
		> .icon-font.t-icon-active {
			display: none;
		}
		&.is-active {
			.icon-font {
				&.t-icon {
					display: none;
				}
				&.t-icon-active {
					display: block;
				}
			}
		}
	}
	:deep(.el-sub-menu) {
		.icon-font.t-icon-active {
			display: none;
		}
		&.is-active {
			> .el-sub-menu__title {
				.icon-font {
					&.t-icon {
						display: none;
					}
					&.t-icon-active {
						display: block;
					}
				}
				.t-text,
				.el-sub-menu__icon-arrow {
					color: #136c4d;
				}
			}
		}
	}
}
</style>
