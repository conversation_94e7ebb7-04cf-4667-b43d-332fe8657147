<template>
	<el-dialog
		v-model="dialogVisible"
		width="1000px"
		:title="title"
		:close-on-click-modal="false"
		append-to-body
		align-center
		destroy-on-close
		@closed="closeDialog"
		@open="handleOpen"
	>
		<div class="content">
			<h1>{{ rowData!.title }}</h1>
			<div class="text">
				{{ rowData!.detail }}
			</div>
		</div>
	</el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { JobBaseVO } from '../api/types';
interface Props {
	type: string; //新增还是查看还是编辑
	rowData?: JobBaseVO | null;
}
const props = withDefaults(defineProps<Props>(), {
	type: 'add',
});
const dialogVisible = defineModel<boolean>('visible');

const title = computed(() => {
	switch (props.type) {
		case 'add':
			return '新增';
		case 'view':
			return '预警详情';
		case 'edit':
			return '编辑';
		default:
			break;
	}
});
const emits = defineEmits<{
	(ev: 'saveForm'): void;
}>();
onMounted(() => {});
function handleOpen() {}
function closeDialog() {
	dialogVisible.value = false;
}

defineExpose({
	open,
	close,
});
</script>
<style lang="scss" scoped>
.content {
	min-height: 200px;
	text-align: center;
	box-sizing: border-box;
	padding: 0 20px;
	.text{
		text-align: left;
		margin-top: 50px;
		font-size: 16px;
	}
}
</style>
