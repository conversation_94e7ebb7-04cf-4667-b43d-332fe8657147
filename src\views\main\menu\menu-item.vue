<template>
	<el-sub-menu
		:index="String(itemObj.name)"
		:key="itemObj.name"
		v-if="hasChildrenHide(itemObj.children)"
		:disabled="!!itemObj.meta?.disabled"
	>
		<template #title>
			<icon-font v-if="itemObj.meta?.icon" class="t-icon" :type="itemObj.meta.icon" />
			<icon-font v-if="itemObj.meta?.activeIcon" class="t-icon-active" :type="itemObj.meta.activeIcon" />
			<span class="t-text" :title="title">{{ title }}</span>
		</template>
		<template v-for="item_ in itemObj.children">
			<menu-item v-if="!item_.meta?.hide" :key="item_.name" :isFirst="false" :itemObj="item_" />
		</template>
	</el-sub-menu>
	<el-menu-item v-else-if="!itemObj.meta?.hide" :index="String(itemObj.name)" :disabled="!!itemObj.meta?.disabled">
		<icon-font v-if="itemObj.meta?.icon" class="t-icon" :type="itemObj.meta.icon" />
		<icon-font v-if="itemObj.meta?.activeIcon" class="t-icon-active" :type="itemObj.meta.activeIcon" />
		<span class="t-text" :title="title">{{ title }}</span>
	</el-menu-item>
</template>

<script lang="ts" setup>
// AsideMenuItem
import { RouteRecordRaw, useRoute } from 'vue-router';
import { isArray } from '@/utils/is';
import { defaultText } from '@/config';

import { computed } from 'vue';
// import {} from '@ant-design/icons-vue';
interface Props {
	itemObj: RouteRecordRaw;
	isFirst?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
	isFirst: true,
});

const title = computed(() => (props.itemObj.meta?.title as string) || defaultText);

const hasChildren = (arr: any) => {
	return isArray(arr) && arr.length;
};
const hasChildrenHide = (arr?: RouteRecordRaw[]) => {
	return hasChildren(arr) && arr!.some((item) => !item.meta?.hide);
};
</script>
<style lang="scss" scoped>
:deep(.icon-font) {
	// color: red;
	color: #000;
	font-size: 16px;
}
:deep(.el-menu-item) {
	&.is-active {
		background-color: #EDF7F4;
	}
}
.t-text {
	margin-left: 16px;
}
</style>
